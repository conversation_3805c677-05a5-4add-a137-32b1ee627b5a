"use client"

import { Database } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import Tooltip from "@/components/ui/tooltip/Tooltip"
import DataProgressBar from "./DataProgressBar"

type FileTypeItemProps = {
  color: string
  type: string
}

function FileTypeItem({ color, type }: FileTypeItemProps) {
  return (
    <div className="flex items-center gap-2">
      <div className={`w-3 h-3 rounded-full`} style={{ backgroundColor: color }}></div>
      <span className="text-sm text-[#333333]">{type}</span>
    </div>
  )
}

type PipelineItemProps = {
  label: string
  count: string
  color?: string
  tooltipText?: string
}

function PipelineItem({ label, count, color = "text-[#333333]", tooltipText }: PipelineItemProps) {
  return (
    <div className="flex justify-between items-center">
      <div className="flex items-center gap-2">
        <span className="text-sm text-[#666666]">{label}</span>
        {tooltipText && <Tooltip text={tooltipText} />}
      </div>
      <span className={`text-sm font-medium ${color}`}>{count}</span>
    </div>
  )
}

export default function DataAssetsSection() {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5" />
          Data Assets
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-8">
          <div>
            <div className="text-center mb-4">
              <div className="text-3xl font-bold text-[#333333]">247</div>
              <div className="text-[#666666] text-sm">Total files</div>
            </div>

            <div className="space-y-3">
              <FileTypeItem color="#a855f7" type="Excel" />
              <FileTypeItem color="#dc2625" type="PDF" />
              <FileTypeItem color="#00b2a1" type="CSV" />
              <FileTypeItem color="#3c82f6" type="Word" />
            </div>
          </div>

          <div>
            <div className="mb-4">
              <h4 className="font-medium text-[#333333] mb-2">Processing Pipeline</h4>
              <p className="text-[#666666] text-sm">Current status of data processing workflows</p>
            </div>

            <div className="space-y-3">
              <PipelineItem
                label="Total Data Ingested"
                count="30 files"
                tooltipText="Connected and ingested into dataplace"
              />
              <PipelineItem
                label="In Process"
                count="11 files"
                color="text-[#3c82f6]"
                tooltipText="Transforming and generating contextual metadata"
              />
              <PipelineItem
                label="Silver Level"
                count="7 files"
                color="text-[#888888]"
                tooltipText="Basic transformations complete"
              />
              <PipelineItem
                label="Gold Level (AI Ready)"
                count="12 files"
                color="text-[#eaa23b]"
                tooltipText="Available for BI tools, ML models, and AI teams"
              />
            </div>

            <div className="mt-4">
              <DataProgressBar
                totalFiles={30}
                inProcessFiles={11}
                silverFiles={7}
                goldFiles={12}
                aiReadyInUseFiles={6}
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
