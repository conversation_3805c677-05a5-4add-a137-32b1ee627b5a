import * as React from "react";
import { cn } from "@/lib/utils";
import { Button } from "../button/button";
import { Search } from "lucide-react";

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onSearchClick?: () => void;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(({ className, type, onSearchClick, ...props }, ref) => {
  return (
    <div className="flex">
      <input
        type={type}
        ref={ref}
        {...props}
        className={cn(
          "flex h-10 w-[583px] rounded-md rounded-r-none border border-input bg-dataPreviewTabsSelectorBg px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        onKeyDown={(e) => {
          if (e.key === "Enter" && onSearchClick) {
            onSearchClick();
          }
        }}
      />
      <Button
        className="h-10 bg-sidebarItemSelected rounded-none rounded-r-md"
        onClick={onSearchClick ? () => onSearchClick() : undefined}
      >
        <Search className="h-4 w-4 text-white rounded-none" />
      </Button>
    </div>
  );
});
Input.displayName = "Input";

export default Input;
