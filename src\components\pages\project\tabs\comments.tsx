"use client"
import { use, useState } from "react"
import { CommentButton } from "@/components/pages/project/Comments/comments-component/commentsSectionUI/button"
import { CommentPanel } from "@/components/pages/project/Comments/comments-component/commentsSectionUI/panel"
import { ProjectDetails } from "@/service/types/types";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";

export default function Comments({ projectDetails }: { projectDetails: ProjectDetails | null }) {
  const [isCommentPanelOpen, setIsCommentPanelOpen] = useState(false)
  const projectCommentCount = useSelector((state: RootState) => state.projectCommentCount.projectCommentCount);

  return (
    <div className="">
      <CommentButton count={projectCommentCount} onClick={() => setIsCommentPanelOpen(true)} />
      <CommentPanel
        isOpen={isCommentPanelOpen}
        onClose={() => setIsCommentPanelOpen(false)}
        projectData={projectDetails}
      />
    </div>
  )
}