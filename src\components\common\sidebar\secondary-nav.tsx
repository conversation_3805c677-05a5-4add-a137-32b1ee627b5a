"use client";

// import { Input } from "@/components/ui/input"
import { Button } from "./sidebar-btn";
import { motion, AnimatePresence } from "framer-motion";
import {
  TbHeart,
  TbLayoutSidebarLeftCollapseFilled,
  TbBulb,
  TbLayoutSidebarLeftExpandFilled,
  TbHeartFilled,
  TbUserCircle,
  TbRocket,
  TbHome,
  TbStar,
} from "react-icons/tb";
import {
  TbDatabase,
  TbChartArrowsVertical,
  TbTriangleSquareCircle,
  TbSettings,
} from "react-icons/tb";
import Link from "next/link";
import { PlusIcon } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { toggleSidebar } from "@/lib/store/features/sidebar/sidebarSlice";
import { RootState } from "@/lib/store";
import { useEffect, useState } from "react";
import { NavItem } from "@/types";
import { User } from "@/service/types/types_ai";
import { setActiveLink } from "@/lib/store/features/activeModule/activeModuleSlice";
import { usePathname } from "next/navigation";

interface SecondaryNavProps {
  activeSection: string;
  [x: string]: any;
}

export function SecondaryNav(props: SecondaryNavProps & { disableValueItems?: boolean }) {
  const {
    activeSection,
    isDialogOpen,
    dialogRef,
    setIsOpen,
    setTitle,
    setIsWorldDataset,
    setIsDialogOpen,
    disableValueItems = false,
  } = props;
  const dispatch = useDispatch();
  const isSidebarOpen = useSelector((state: RootState) => state.sidebar.isOpen);
  const favorites = useSelector((state: RootState) => state.favorites.items);
  const userInfo = useSelector((state: any) => state.user.userInfo);
  //const [activeLink,setActiveLink] = useState("")
  const activeLink = useSelector((state: RootState) => state.activeLink.value);
  const pathname = usePathname();
  const valuePillars = useSelector((state: any) => state.valuePillars.pillars);

  const hasAccess = (user: any): boolean => {
    if (user.user.isSysAdmin) {
      return true;
    } else {
      return user.user.department.some(
        (department: { departmentRole: string }) =>
          department.departmentRole === "admin" ||
          department.departmentRole === "editor"
      );
    }
  };

  const showButton = hasAccess(userInfo);

  const [favItems, setFavItems] = useState<NavItem[]>([]);
  useEffect(() => {
    const mappedFavorites = favorites.map(
      (fav: any) => ({
        label: fav.name,
        href: fav.href,
        key: fav.id,
        isAiBox: fav.isAiBox,
        appUrl: fav.appUrl,
      })
    );
    setFavItems(mappedFavorites);
  }, [favorites]);


  const sections = {
    data: {
      title: "Data",
      icon: TbDatabase,
      items: [
        { label: "World Data", href: "/data/world-data" },
        { label: "Company Data", href: "/data/company-data" },
        { label: "Platform Connection", href: "/data/platform-connection" },
        // { label: "Governance Insights", href: "/data/governance" },
      ],
      addButton: "+ Add Data Asset",
      searchPlaceholder: "Search Data...",
      href: "/data",
    },
    home: {
      title: "Home",
      icon: TbHome,
      href: "/",
    },
    value: {
      title: "Value Analytics",
      icon: TbChartArrowsVertical,
      items: [
        { label: "Operational Efficiency", href: "/operational-efficiency" },
        { label: "Cost Reduction", href: "/cost-reduction" },
        { label: "Revenue Growth", href: "/revenue-growth" },
        { label: "Customer Experience", href: "/customer-experience" },
      ],
      searchPlaceholder: "Search...",
      href: "/value",
    },
    opportunities: {
      title: "Opportunities",
      icon: TbBulb,
      searchPlaceholder: "Search Opportuniti...",
      href: "/explore-opportunities",
    },
    apps: {
      title: "Apps",
      icon: TbRocket,
      pinnedItems: favItems,
      items: [
        { label: "Governance Insights", href: "/apps/governance" },
      ],
      searchPlaceholder: "Search Deployed Projects...",
      href: "/apps",
    },
    projects: {
      title: "Projects",
      icon: TbTriangleSquareCircle,
      // pinnedItems: favItems,
      // sections: {
      //   "Deployed Projects": [],
      // },
      searchPlaceholder: "Search Projects...",
      href: "/projects",
    },
    profile: {
      title: "Profile",
      icon: TbUserCircle,
      href: "/profile",
    },
    // settings: {
    //   title: "Settings",
    //   icon: TbSettings,
    //   items: ["Infra Analytics Cost", "Configuration", "Deployments", "Manage Users", "Account"],
    //   searchPlaceholder: "Search...",
    // },
  };

  const handleOnBtnClick = (title: string) => {
    if (title === "Data") setIsDialogOpen(!isDialogOpen);
  };

  const handleToggleSidebar = () => {
    dispatch(toggleSidebar(!isSidebarOpen));
  };

  const activeContent: any = sections[activeSection as keyof typeof sections];

  if (!activeContent) return null;

  // These control how the sidebar and its content animate
  const sidebarVariants = {
    expanded: { width: 240, transition: { duration: 0.3, ease: "easeInOut" } },
    collapsed: { width: 80, transition: { duration: 0.3, ease: "easeInOut" } },
  };

  const contentVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delay: 0.2, // Delay content appearance until sidebar has expanded
        duration: 0.2,
      },
    },
    exit: {
      opacity: 0,
      transition: {
        duration: 0.1,
      },
    },
  };

  return (
    <div className="flex flex-col h-full ml-[80px]">
      {/* Replace the outer div with a motion.div for width animation */}
      <motion.div
        className="flex flex-col h-full fixed"
        initial={isSidebarOpen ? "expanded" : "collapsed"}
        animate={isSidebarOpen ? "expanded" : "collapsed"}
        variants={sidebarVariants}
      >
        {/* Wrap content in AnimatePresence to handle mounting/unmounting */}
        <AnimatePresence mode="wait">
          {isSidebarOpen ? (
            /* Expanded content with animations */
            <motion.div
              className="flex flex-col h-full w-full"
              key="expanded-content"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={contentVariants}
            >
              <div className="px-4 pt-6 pb-2">
                <Link
                  href={activeContent?.href || "/"}
                  onClick={() =>
                    dispatch(setActiveLink(activeContent?.href || "/"))
                  }
                >
                  <h2
                    className={`text-xl font-bold flex items-center gap-2 pb-1 mx-2 ${activeLink === activeContent.href
                      ? "border-b-2 border-[#00B2A1]"
                      : ""
                      }`}
                  >
                    {activeContent.icon && (
                      <activeContent.icon className="w-6 h-6" />
                    )}
                    {activeContent.title}
                  </h2>
                </Link>
              </div>

              <div className="flex-1 overflow-y-auto">
                {activeContent.pinnedItems && (
                  <div className="px-2 py-4">
                    {activeContent.pinnedItems.map(
                      (item: any, index: number) => (
                        <div
                          key={item.key || item.href || index}
                          className="flex items-center justify-between py-2 mx-2"
                        >
                          {typeof item === "string" ? (
                            <span className="text-base font-bold">{item}</span>
                          ) : (
                            <Link
                              href={`${item.href}?name=${item.label}&isAiBox=${item.isAiBox}&appUrl=${item.appUrl}`}
                              className={` w-[165px] ${activeLink === item.href
                                ? "text-teal-500 underline font-bold "
                                : "text-base font-bold hover:text-[#00B2A1]"
                                }`}
                              onClick={() => dispatch(setActiveLink(item.href))}
                            >
                              {item.label}
                            </Link>
                          )
                          }
                          <TbHeartFilled className="w-4 h-4 text-teal-500" />
                        </div>
                      )
                    )}
                  </div>
                )}

                {activeContent.items && (
                  <div className="px-2 py-4 mx-2">
                    {activeContent.items.map((item: any, index: number) => (
                      <div
                        key={item.label || item.href || index}
                        className="py-2"
                      >
                        {typeof item === "string" ? (
                          <span className="text-base font-semibold">
                            {item}
                          </span>
                        ) : (
                          <>
                            {item.label === "Governance Insights" && (
                              <div className="w-full border border-dashed border-gray-500 mb-2"></div>
                            )}
                            {activeContent.title === "Value Analytics" && disableValueItems ? (
                              <span
                                className="flex items-center gap-2 font-semibold rounded text-gray-400 cursor-not-allowed opacity-60"
                              >
                                {item.icon && <item.icon className="w-5 h-5" />}
                                {item.label}
                              </span>
                            ) : (
                              <Link
                                href={item.href}
                                onClick={() => {
                                  // Removed debug console.log
                                }}
                                className={` ${pathname === item.href
                                  ? "text-[#00B2A1] underline font-semibold"
                                  : "hover:text-[#00B2A1]"
                                  } flex items-center gap-2 font-semibold rounded transition-colors`}
                              >
                                {item.icon && <item.icon className="w-5 h-5" />}
                                {item.label}
                              </Link>
                            )}
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {activeContent.sections && (
                  <>
                    {Object.entries(activeContent.sections).map(
                      ([title, items]: any) => (
                        <div key={title} className="mt-4 font-bold">
                          {title === "Deployed Projects" ? (
                            <Link
                              href={"/apps/deployed-projects"}
                              className="px-4 py-4 border-t border-b font-bold border-dashed border-gray-200"
                            >
                              <span className="text-base">{title}</span>
                            </Link>
                          ) : (
                            <div className="px-4 py-4 border-t font-bold border-b border-dashed border-gray-200">
                              <span className="text-base">{title}</span>
                            </div>
                          )
                          }
                          <div className="px-4 py-2">
                            {items.map((item: any, index: number) => (
                              <div
                                key={item.label || item.href || index}
                                className="py-2"
                              >
                                <span className="text-base font-bold">
                                  {item}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )
                    )}
                  </>
                )}

                {activeContent.addButton && showButton && (
                  <div
                    data-dropdown-button
                    className="relative font-bold mx-4 py-1 border-t-2 border-b-2 border-dashed border-[#00B2A1]"
                  >
                    <Button
                      onClick={() => handleOnBtnClick(activeContent.title)}
                      variant="outline"
                      className="w-full border-1 border-gray-900 bg-transparent justify-start rounded-full font-bold text-base px-0 hover:bg-transparent"
                    >
                      {activeContent.addButton}
                    </Button>
                    {isDialogOpen && (
                      <div
                        ref={dialogRef}
                        className="absolute left-0 mt-3  top-7 w-40 bg-white shadow-lg rounded-sm py-2 text-black z-10"
                      >
                        <div
                          className="flex items-center gap-2 px-4 py-2 text-[#3B4154] hover:bg-gray-200 rounded cursor-pointer"
                          onClick={() => {
                            setIsOpen(true);
                            setTitle("Add World Data");
                            setIsWorldDataset(true);
                          }}
                        >
                          <PlusIcon className="w-5 h-5 text-[#888FAA]" />
                          <span className="text-sm">World Data</span>
                        </div>
                        <div
                          className="flex items-center gap-2 px-4 py-2 text-[#3B4154] hover:bg-gray-200 rounded cursor-pointer"
                          onClick={() => {
                            setIsOpen(true);
                            setTitle("Add Company Data");
                            setIsWorldDataset(false);
                          }}
                        >
                          <PlusIcon className="w-5 h-5 text-[#888FAA]" />
                          <span className="text-sm">Company Data</span>
                        </div>
                        <div
                          className="flex items-center gap-2 px-4 py-2 text-[#3B4154] hover:bg-gray-200 rounded cursor-pointer"
                          onClick={() => {
                            setIsOpen(true);
                            setTitle("Add Document Data");
                            setIsWorldDataset(false);
                          }}
                        >
                          <PlusIcon className="w-5 h-5 text-[#888FAA]" />
                          <span className="text-sm">Documents</span>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="flex flex-col gap-2 p-4 mb-2">
                <div className="flex justify-center items-center">
                  <img
                    src={process.env.NEXT_PUBLIC_USER_LOGO}
                    alt="Company Logo"
                    className="h-8"
                    onError={(e) => {
                      e.currentTarget.src = "/assets/Group%204355.svg";
                    }}
                  />
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-[11px] text-gray-400 self-center">
                    Intelligence tech by{" "}
                    <Link
                      target="_blank"
                      href={"https://sutra.ai"}
                      className="text-[#00B2A1]"
                    >
                      Sutra.AI
                    </Link>{" "}
                  </p>
                  <TbLayoutSidebarLeftCollapseFilled
                    onClick={handleToggleSidebar}
                    className="w-6 h-6 text-gray-400 hover:cursor-pointer"
                  />
                </div>
              </div>
            </motion.div>
          ) : (
            <motion.div
              className="flex flex-col h-full w-full"
              key="collapsed-content"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={contentVariants}
            >
              <div className="flex justify-center items-center pt-6">
                {activeContent.icon && (
                  <activeContent.icon className="w-8 h-8 text-black" />
                )}
              </div>
              <div className="flex-1"></div>
              <div className="flex justify-center p-4 mb-2">
                <TbLayoutSidebarLeftExpandFilled
                  onClick={handleToggleSidebar}
                  className="w-6 h-6 text-gray-400 hover:cursor-pointer"
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
}
