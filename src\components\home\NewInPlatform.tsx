"use client";

import { TbStar } from "react-icons/tb";
import { useEffect, useState } from "react";
import { getWhatsNew, WhatsNewItem } from "@/service/homepageService";

export function NewInPlatform() {
  const [whatsNew, setWhatsNew] = useState<[WhatsNewItem] | []>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchWhatsNew = async () => {
      try {
        const whatsNewResponse = await getWhatsNew();
        setWhatsNew(whatsNewResponse);
      } catch (error) {
        console.error('Error fetching pillar data:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchWhatsNew();
  }, []);
  function ItemCardShimmer() {
    return (
      <div className="block p-4 border border-gray-200 rounded-lg animate-pulse">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-gray-200 rounded" />
            <div className="h-4 w-32 bg-gray-200 rounded" />
          </div>
          <div className="h-3 w-20 bg-gray-200 rounded" />
        </div>
        <div className="h-5 bg-gray-200 rounded w-3/4 mb-2" />
        <div className="space-y-1">
          <div className="h-3 bg-gray-200 rounded w-full" />
          <div className="h-3 bg-gray-200 rounded w-[90%]" />
          <div className="h-3 bg-gray-200 rounded w-[80%]" />
        </div>
      </div>
    );
  }

  return (
    <div className="border border-gray-200 rounded-xl bg-white shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <TbStar className="text-2xl" />
          <h2 className="text-xl font-semibold text-[#111827]">
            What&apos;s New?
          </h2>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {loading ? (
          Array.from({ length: 3 }).map((_, idx) => <ItemCardShimmer key={idx} />)
        ) : (
          whatsNew.map((item) => {
            return (
              <div key={item._id}>
                <div className="block p-4 border border-gray-200 rounded-lg transition-all">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      {getItemIcon(item.category)}
                      <span className="text-sm font-medium ml-2 text-[#4b5563]">{item.title}</span>
                    </div>
                    <span className="text-xs text-[#6b7280]">
                      {new Date(item.updatedAt).toLocaleDateString("en-GB", {
                        day: "2-digit",
                        month: "short",
                        year: "numeric",
                      })}
                    </span>
                  </div>
                  <h3 className="font-medium text-[#111827] mb-1">{item.header}</h3>
                  <p className="text-sm text-[#4b5563] line-clamp-3">{item.description}</p>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
}

function getItemTypeLabel(type: string): string {
  switch (type) {
    case "opportunity":
      return "New Opportunity";
    case "project":
      return "Project Finished";
    case "feature":
      return "New Feature";
    default:
      return "New Item";
  }
}

function getItemIcon(type: string) {
  switch (type) {
    case "Opportunity":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 text-[#00B2A1]"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
            clipRule="evenodd"
          />
        </svg>
      );
    case "Notifications":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 text-[#00B2A1]"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
            clipRule="evenodd"
          />
        </svg>
      );
    case "Feature":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 text-[#00B2A1]"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z" />
        </svg>
      );
    default:
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 text-gray-400"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
            clipRule="evenodd"
          />
        </svg>
      );
  }
}
