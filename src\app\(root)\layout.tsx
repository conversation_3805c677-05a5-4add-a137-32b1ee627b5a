"use client";
import MainContent from "@/components/ui/MainContent/MainContent";
import { SidebarProvider } from "@/context/sidebar-context";
import AuthWrapper from "./AuthWrapper";
import IntercomWrapper from "@/components/common/intercom/IntercomProvider";
import { Sidebar as SidebarV2 } from "@/components/common/sidebar";
import SearchBar from "@/components/ui/search-bar";
import ScrollToTop from "@/components/common/ScrollToTop/ScrollToTop";
import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { initializeGA, trackPageView } from "@/service/analytics";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();
  const [isMobile, setIsMobile] = useState(false);

  // useEffect(() => {
  //   initializeGA();
  //   trackPageView(pathname);
  // }, [pathname]);

  useEffect(() => {
    // Function to check if viewport is mobile
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768); // 768px is a common breakpoint for mobile
    };

    // Check on initial load
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener("resize", checkIfMobile);

    // Cleanup event listener on component unmount
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  return (
    <SidebarProvider>
      <AuthWrapper>
        <IntercomWrapper>
          {/* Only show sidebar on desktop */}
          {!isMobile && <SidebarV2 />}

          {/* Only show SearchBar on desktop */}
          {!isMobile && <SearchBar />}
          <ScrollToTop />
          <MainContent>{children}</MainContent>
        </IntercomWrapper>
      </AuthWrapper>
    </SidebarProvider>
  );
}
