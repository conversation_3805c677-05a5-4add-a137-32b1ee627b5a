import React from "react";

interface StepIndicatorProps {
  currentStep: number;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({ currentStep }) => {
  return (
    <div className="flex flex-col mb-6">
      {/* Steps Container */}
      <div className="flex items-center">
        {/* Step 1: Basic Details */}
        <div className="flex items-center">
          <div
            className={`w-4 h-4 rounded-full flex items-center justify-center
              ${
                currentStep >= 1
                  ? "bg-teal-500 text-white"
                  : "border-2 border-gray-300"
              }`}
          >
            {currentStep > 1 ? "✓" : ""}
          </div>
        </div>

        {/* Connecting Line */}
        <div
          className={`w-8 h-0.5 ${
            currentStep > 1 ? "bg-teal-500" : "bg-gray-200"
          }`}
        />

        {/* Step 2: Add Data */}
        <div className="flex items-center justify-center gap-2">
          <div
            className={`w-4 h-4 rounded-full flex items-center justify-center bg-gray-300
              ${
                currentStep >= 2
                  ? "bg-teal-500 text-white"
                  : "border-2 border-gray-300"
              }`}
          >
            {currentStep > 2 ? "✓" : ""}
          </div>
        </div>

        {/* Step Label */}
        <span className="text-base text-black font-medium ml-2">
          {currentStep === 1 ? "Basic Details" : "Add Data"}
        </span>
      </div>

      <div className="w-full border-b border-[1.2px] border-gray-300 mt-5"></div>
    </div>
  );
};

export default StepIndicator;
