"use client";
import React, { useState, useEffect } from "react";
import { useSearch<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Tile from "@/components/common/tile/tile";
import Loader from "@/components/common/loader/loader";
import Filter from "@/components/common/filter/filter";
import Sort from "@/components/common/sort/sort";
import { Pagination } from "@/components/common/pagination/pagination";
import { dataService } from "@/service/dataService";
import { getProjects } from "@/service/projects";
import { Collaborator } from "@/service/types/types";
import { getAiBoxes, getAiBoxResponse } from "@/service/aiServices";
import { useDispatch } from "react-redux";
import { setProjectActiveTab } from "@/lib/store/features/project/projectTabSlice";
import { setActiveAssetTab } from "@/lib/store/features/project/projectAssetsSlice";
import { setActiveManageTab } from "@/lib/store/features/project/projectManageSlice";

interface Filters {
  tags: string[];
  fileTypes: string[];
  licenses: string[];
}

const statusStyles: Record<string, { text: string; bg: string }> = {
  Draft: { text: "#FFFFFF", bg: "#888FAA" },
  Hold: { text: "#666F8F", bg: "#CFD2DE" },
  "In-process": { text: "#3B4154", bg: "#EAA23B" },
  Operational: { text: "#FFFFFF", bg: "#018E42" },
};

export default function Page() {
  const router = useRouter();
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const query = searchParams.get("query")?.toString() || "";
  const [activeTab, setActiveTab] = useState("World Data");

  const [currentPage, setCurrentPage] = useState(1);
  const [sortValue, setSortValue] = useState<string>("views");
  const [sortOrder, setSortOrder] = useState<number>(-1);

  const [exploreUrlQuery, setExploreUrlQuery] = useState({
    page: 1,
    limit: 10,
    query: query,
  });
  const [companyUrlQuery, setCompanyUrlQuery] = useState({
    page: 1,
    limit: 10,
    search: query,
    tags: "",
    license: "",
    fileTypes: "",
    sortBy: "updatedAt",
    sortOrder: -1,
  });
  const [projectUrlQuery, setProjectUrlQuery] = useState({
    page: 1,
    limit: 10,
    query: query,
  });

  const [exploreData, setExploreData] = useState<any>(null);
  const [exploreDataCount, setExploreDataCount] = useState(0);

  const [companyData, setCompanyData] = useState<any>(null);
  const [companyDataCount, setCompanyDataCount] = useState(0);

  const [projectData, setProjectData] = useState<any>(null);
  const [aiProjectData, setAiProjectData] = useState<getAiBoxResponse | null>(null);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [selectedFilters, setSelectedFilters] = useState<Filters>({
    tags: [],
    fileTypes: [],
    licenses: [],
  });

  useEffect(() => {
    setExploreUrlQuery((prev) => ({ ...prev, page: currentPage }));
    setCompanyUrlQuery((prev) => ({ ...prev, page: currentPage }));
    setProjectUrlQuery((prev) => ({ ...prev, page: currentPage }));
  }, [currentPage]);

  useEffect(() => {
    setCompanyUrlQuery((prev) => ({
      ...prev,
      tags: selectedFilters.tags.join(","),
      license: selectedFilters.licenses.join(","),
      fileTypes: selectedFilters.fileTypes.join(","),
    }));
  }, [selectedFilters]);

  useEffect(() => {
    setCompanyUrlQuery((prev) => ({
      ...prev,
      sortBy: sortValue,
      sortOrder: sortOrder,
    }));
  }, [sortValue, sortOrder]);

  useEffect(() => {
    setExploreUrlQuery((prev) => ({ ...prev, query }));
    setCompanyUrlQuery((prev) => ({ ...prev, search: query }));
    setProjectUrlQuery((prev) => ({ ...prev, query }));
  }, [query]);

  const fetchAllData = async () => {
    setLoading(true);
    setError(null);
    try {
      const [explore, company, projects, aiProjects] = await Promise.all([
        dataService.getExploredData(exploreUrlQuery),
        dataService.getCompanyData(companyUrlQuery),
        getProjects(projectUrlQuery),
        getAiBoxes(query),
      ]);

      setExploreData(explore);
      setExploreDataCount(explore?.worldData?.count || 0);

      setCompanyData(company);
      setCompanyDataCount(company?.pagination?.total || 0);
      setProjectData(projects.projects);
      setAiProjectData(aiProjects);
    } catch {
      setError("Failed to fetch data.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAllData();
  }, [exploreUrlQuery, companyUrlQuery, projectUrlQuery]);

  // Sample handler for "View Dataset" in Company tab
  const handleViewDataset = (id: string) => {
    router.push(`/data/company-data/dataset/${id}`);
  };

  // Sample handler for "Go to Project" in Projects tab
  const handleProjectViewDataset = (id: string) => {
    dispatch(setActiveAssetTab("All Assets"));
    dispatch(setActiveManageTab("General"));
    dispatch(setProjectActiveTab("Overview"));
    router.push(`/projects/dashboard/${id}`);
  };

  const hasFilters = Object.values(selectedFilters).some((arr) => arr.length > 0);
  const removeFilter = (category: keyof Filters, value: string) => {
    setSelectedFilters((prevFilters) => ({
      ...prevFilters,
      [category]: prevFilters[category].filter((item) => item !== value),
    }));
  };

  if (loading) {
    return <Loader />;
  }

  const NoDataPlaceholder = ({ message }: { message: string }) => (
    <div className="flex flex-col gap-2 justify-center items-center mt-20">
      <img src="/placeholder.svg" alt="No data" className="w-36 h-36 text-gray-700 object-contain" />
      <h1 className="text-lg text-gray-400 mt-4">{message}</h1>
    </div>
  );

  return (
    <>
      <div className="p-8">
        <h1 className="text-2xl font-semibold mb-3 ml-3 text-gray-700">Results for “{query}”</h1>

        <div>
          <div className="flex justify-between items-center border-b">
            <div className="flex">
              {["World Data", "Company", "Projects", "AI in a Box", "Documents"].map((tab) => (
                <button
                  key={tab}
                  className={`px-4 py-2 text-sm text-gray-700 border-b-2 border-transparent focus:outline-none ${activeTab === tab ? "font-semibold border-b-teal-500" : ""
                    }`}
                  onClick={() => setActiveTab(tab)}
                >
                  {tab} {tab === "World Data" && <span>({exploreDataCount})</span>}
                  {tab === "Company" && <span>({companyDataCount})</span>}
                  {tab === "Projects" && (
                    <span>({projectData?.length || 0})</span>
                  )}
                  {tab === "AI in a Box" && (
                    <span>({aiProjectData?.data?.length || 0})</span>
                  )}
                  {tab === "Documents" && <span>(0)</span>}
                </button>
              ))}
            </div>

            {activeTab === "Company" && (
              <div className="flex items-center space-x-4">
                <Filter
                  filters={companyData?.filters}
                  selectedFilters={selectedFilters}
                  setSelectedFilters={setSelectedFilters}
                />
                <Sort
                  sortValue={sortValue}
                  setSortValue={setSortValue}
                  sortOrder={sortOrder}
                  setSortOrder={setSortOrder}
                />
              </div>
            )}
          </div>

          <div className="p-4 text-sm">
            {/* World Data Tab */}
            {activeTab === "World Data" && (
              <>
                {exploreData?.worldData?.count > 0 ? (
                  <div className="flex flex-col gap-5">
                    {exploreData?.worldData?.data.map((dataset: any) => (
                      <Tile
                        key={dataset.did}
                        title={dataset.title}
                        description={dataset.description}
                        tags={dataset.filetypes}
                        button_text="Import"
                        onButtonClick={() => {
                          // Perform import action if needed
                        }}
                        id={dataset.did}
                        isImportedData={dataset.isImported}
                      />
                    ))}
                  </div>
                ) : (
                  <NoDataPlaceholder message="No data was found based on the keyword searched." />
                )}
              </>
            )}

            {/* Company Tab */}
            {activeTab === "Company" && (
              <>
                {hasFilters && (
                  <div className="flex flex-wrap gap-2 mt-6 rounded-lg items-center">
                    {Object.entries(selectedFilters).map(([category, values]) => (
                      <div key={category} className="flex gap-2 items-center">
                        {values.length > 0 && <h1>{category.charAt(0).toUpperCase() + category.slice(1)}: </h1>}
                        {values.map((value: any) => (
                          <div
                            key={`${category}-${value}`}
                            className="flex items-center gap-2 bg-[#F4F5F6] text-[#3B4154] px-3 py-1 rounded-md text-sm"
                          >
                            <div>{value}</div>
                            <button
                              onClick={() => removeFilter(category as keyof Filters, value)}
                              className="text-[#888FAA] text-sm"
                            >
                              ✖
                            </button>
                          </div>
                        ))}
                      </div>
                    ))}
                    <button
                      className="text-md text-teal-500 bg-white"
                      onClick={() =>
                        setSelectedFilters({
                          tags: [],
                          fileTypes: [],
                          licenses: [],
                        })
                      }
                    >
                      Clear all
                    </button>
                  </div>
                )}

                {companyData?.datasets?.length > 0 ? (
                  <div className="flex flex-col gap-5">
                    {companyData?.datasets.map((dataset: any) => (
                      <Tile
                        key={dataset._id}
                        title={dataset.title}
                        description={dataset.description}
                        tags={dataset.fileTypes}
                        button_text="View Dataset"
                        onButtonClick={() => handleViewDataset(dataset._id)}
                      />
                    ))}
                  </div>
                ) : (
                  <NoDataPlaceholder message="No data was found based on the keyword searched." />
                )}
              </>
            )}
            {/* Projects Tab */}
            {activeTab === "Projects" && (
              <>
                {projectData?.length > 0 ? (
                  <div className="flex flex-col gap-5">
                    {projectData.map((dataset: any) => (
                      <div key={dataset._id} className="text-sm">
                        <div className="flex justify-between items-center border-b-2 pb-5 gap-2">
                          <div className="flex flex-col gap-2">
                            <div className="flex gap-2 items-center w-full">
                              <p className="text-lg font-semibold text-gray-700">{dataset.name}</p>
                              <span
                                className="inline-flex items-center px-5 rounded"
                                style={{
                                  backgroundColor: statusStyles[dataset.status]?.bg || "#6B7280",
                                  color: statusStyles[dataset.status]?.text || "#FFFFFF",
                                }}
                              >
                                <span
                                  className="w-2 h-2 rounded-full mr-1 shrink-0"
                                  style={{
                                    backgroundColor: statusStyles[dataset.status]?.text || "#FFFFFF",
                                  }}
                                ></span>
                                {dataset.status}
                              </span>
                            </div>
                            <div className="flex gap-5 items-center">
                              <div>
                                <span className="text-[#3B4154] font-semibold">Date Created:</span>{" "}
                                {new Date(dataset.createdOn).toLocaleDateString("en-GB", {
                                  day: "2-digit",
                                  month: "short",
                                  year: "numeric",
                                })}
                              </div>
                              <div>
                                <span className="text-[#3B4154] font-semibold">Creator:</span> {dataset.creator.name}
                              </div>
                              <div className="flex gap-1 items-center">
                                <span className="text-[#3B4154] font-semibold">Collaborators:</span>
                                {dataset.collaborators.length > 0 ? (
                                  <div className="flex space-x-1 overflow-hidden">
                                    {dataset.collaborators.map((collab: Collaborator, i: number) => (
                                      <p key={i} className="text-black">
                                        {collab.userId?.name || "N/A"}
                                        {i !== dataset.collaborators.length - 1 && ","}
                                      </p>
                                    ))}
                                  </div>
                                ) : (
                                  "N/A"
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex justify-end items-center gap-2">
                            <button
                              className="px-5 py-2 bg-teal-500 text-white rounded-md text-md"
                              onClick={() => handleProjectViewDataset(dataset._id)}
                            >
                              Go to Project
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <NoDataPlaceholder message="No project was found based on the keyword searched." />
                )}
              </>
            )}
            {/* Documents Tab */}
            {activeTab === "AI in a Box" && (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {(aiProjectData?.data ?? []).length > 0 ? (
                  aiProjectData?.data?.map((card, index) => (
                    <div
                      key={index}
                      onClick={() =>
                        router.push(`/projects/ai-project/${card._id}`)
                      }
                      className="min-w-[260px] bg-white p-4 rounded-[15px] border border-[#CFD2DE] cursor-pointer hover:border-[#00B2A1]"
                    >
                      <img
                        className="object-cover"
                        src={card.thumbnailUrl}
                        height={50}
                        width={50}
                        alt="Thumbnail"
                      />
                      <h3 className="font-semibold text-lg mt-2 mb-1">
                        {card.name}
                      </h3>
                      <p className="text-gray-500 text-sm mt-1">
                        {card.description}
                      </p>
                    </div>
                  ))
                ) : (
                  <div className="text-center col-span-full text-gray-500">
                    <NoDataPlaceholder message="No data was found based on the keyword searched." />
                  </div>
                )}
              </div>

            )}

            {/* Documents Tab */}
            {activeTab === "Documents" && (
              <NoDataPlaceholder message="No data was found based on the keyword searched." />
            )}
          </div>
        </div>
      </div>

      {/* Footer / Pagination */}
      <div className="flex justify-between items-center px-8 pb-8">
        {activeTab === "World Data" && exploreDataCount > 0 && (
          <div className="text-gray-600 text-sm">
            Showing {exploreData?.worldData?.data?.length} of {exploreDataCount}
          </div>
        )}
        {activeTab === "Company" && companyDataCount > 0 && (
          <div className="text-gray-600 text-sm">
            Showing {companyData?.datasets?.length} of {companyDataCount}
          </div>
        )}
        {activeTab === "Projects" && projectData?.length > 0 && (
          <div className="text-gray-600 text-sm">
            Showing {projectData?.length} of {projectData?.length}
          </div>
        )}

        {activeTab === "World Data" && exploreDataCount > 0 && (
          <Pagination
            totalPages={Math.ceil((exploreData?.worldData?.count ?? 0) / 10)}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
          />
        )}
        {activeTab === "Company" && companyDataCount > 0 && (
          <Pagination
            totalPages={companyData?.pagination?.totalPages}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
          />
        )}
        {activeTab === "Projects" && projectData?.length > 0 && (
          <Pagination
            totalPages={Math.ceil((projectData?.length ?? 0) / 10)}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
          />
        )}
      </div>
    </>
  );
}
