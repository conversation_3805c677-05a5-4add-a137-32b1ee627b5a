"use client"

import React, { useState, useEffect } from "react"
import {
    ResponsiveContainer,
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    Bar<PERSON>hart,
    Rectangle,
    Bar,
} from "recharts"

interface GraphPreviewProps {
    filename: string
    previewDataResponse: {
        data: any
        status: number
    }
    headers: string[]
    parsedRows: Record<string, any>[]
}

const GraphPreview: React.FC<GraphPreviewProps> = ({
    filename,
    previewDataResponse,
    headers,
    parsedRows
}) => {
    const fileExtension = filename.split(".").pop()?.toLowerCase()
    const rawData = previewDataResponse?.data

    // --- Dropdown state for axes ---
    const [selectedXAxis, setSelectedXAxis] = useState<string>("")
    const [selectedYAxis, setSelectedYAxis] = useState<string>("")
    const [chartType, setChartType] = useState<string>("Line Chart")

    useEffect(() => {
        // When the file changes, reset the selections
        setSelectedXAxis("")
        setSelectedYAxis("")
    }, [filename])

    useEffect(() => {
        if (headers.length > 0) {
            setSelectedXAxis((prev) => (prev ? prev : headers[0]))
            setSelectedYAxis((prev) =>
                prev ? prev : headers.length > 1 ? headers[1] : headers[0]
            )
        }
    }, [headers])

    // --- Build chart data based on the selected axes ---
    const chartData = parsedRows.map((row) => {
        const xValue = row[selectedXAxis]
        const rawYValue = row[selectedYAxis]
        const yValue = parseFloat(rawYValue)
        return {
            [selectedXAxis]: xValue,
            [selectedYAxis]: isNaN(yValue) ? 0 : yValue,
        }
    })

    return (
        <div className="p-4">
            <div className="flex flex-wrap gap-4 mb-4">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        X Axis
                    </label>
                    <select
                        value={selectedXAxis}
                        onChange={(e) => setSelectedXAxis(e.target.value)}
                        className="p-2 border border-gray-300 rounded"
                    >
                        {headers.map((header) => (
                            <option key={header} value={header}>
                                {header}
                            </option>
                        ))}
                    </select>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Y Axis
                    </label>
                    <select
                        value={selectedYAxis}
                        onChange={(e) => setSelectedYAxis(e.target.value)}
                        className="p-2 border border-gray-300 rounded"
                    >
                        {headers.map((header) => (
                            <option key={header} value={header}>
                                {header}
                            </option>
                        ))}
                    </select>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Chart Type
                    </label>
                    <select
                        value={chartType}
                        onChange={(e) => setChartType(e.target.value)}
                        className="p-2 border border-gray-300 rounded"
                    >
                        {["Line Chart", "Bar Chart"].map((header) => (
                            <option key={header} value={header}>
                                {header}
                            </option>
                        ))}
                    </select>
                </div>
            </div>
            <div className="w-full h-96">
                <ResponsiveContainer width="100%" height="100%">
                    {chartType === "Line Chart" ? (<LineChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey={selectedXAxis} />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey={selectedYAxis} stroke="#8884d8" activeDot={{ r: 8 }} />
                    </LineChart>) :
                        (<BarChart data={chartData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey={selectedXAxis} />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey={selectedYAxis} fill="#8884d8" activeBar={<Rectangle fill="pink" stroke="blue" />} />
                        </BarChart>)}
                </ResponsiveContainer>
            </div>
        </div>
    )
}

export default GraphPreview
