import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// interface UserInfo {
//   id?: string;
//   email?: string;
//   name?: string;
// }

interface UserState {
  isAuthenticated: boolean;
  token: string | null;
  expiresAt: number | null; // Unix timestamp in milliseconds
  userInfo: any;
}

const initialState: UserState = {
  isAuthenticated: false,
  token: null,
  expiresAt: null,
  userInfo: null,
};

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    login: (
      state,
      action: PayloadAction<{ token: string; duration: number }>
    ) => {
      state.isAuthenticated = true;
      state.token = action.payload.token;
      state.expiresAt = Date.now() + action.payload.duration;
    },
    logout: (state) => {
      state.isAuthenticated = false;
      state.token = null;
      state.expiresAt = null;
      state.userInfo = null;
      localStorage.clear();
      localStorage.removeItem("returnUrl");
    },
    checkAuthExpiration: (state) => {
      if (state.expiresAt && Date.now() > state.expiresAt) {
        state.isAuthenticated = false;
        state.token = null;
        state.expiresAt = null;
      }
    },
    setUserInfo: (state, action: PayloadAction<any>) => {
      state.userInfo = action.payload;
    },
    // updateUserInfo: (state, action: PayloadAction<any>) => {
    //   state.userInfo = { ...state.userInfo, user: { ...state.userInfo.user, ...action.payload } };
    // }, NOT BEING USED ANYWHERE
  },
});

export const { login, logout, checkAuthExpiration, setUserInfo } =
  userSlice.actions;
export default userSlice.reducer;
