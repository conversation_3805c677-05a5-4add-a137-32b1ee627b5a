"use client";
import React, { useState } from "react";
import UsersSection from "@/components/users/UsersSection";
import DepartmentsSection from "@/components/department/DepartmentsSection";

const tabs = ["Users", "Departments"];

export default function ManagePage() {
  const [activeTab, setActiveTab] = useState("Users");

  return (
    <div
      className="p-6 bg-gray-50 rounded-xl"
      style={{ height: "calc(100vh - 6rem)", overflowY: "auto" }}
    >
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">Manage</h1>

        <div className="flex justify-between border-y mt-4">
          <div className="flex space-x-4">
            {tabs.map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-8 py-2 text-sm ${activeTab === tab
                  ? "border-b-2 border-[#00B2A1] text-[#3B4154] font-semibold"
                  : "text-[#3B4154]"
                  }`}
              >
                {tab}
              </button>
            ))}
          </div>
        </div>

        <div className="mt-6">
          {activeTab === "Users" && <UsersSection />}
          {activeTab === "Departments" && <DepartmentsSection />}
        </div>
      </div>
    </div>
  );
}
