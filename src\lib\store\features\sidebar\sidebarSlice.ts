import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SidebarState {
    isOpen: boolean;
    activeSection: string;
}

const initialState: SidebarState = {
    isOpen: false,
    activeSection: 'value',
};

const sidebarSlice = createSlice({
    name: 'sidebar',
    initialState,
    reducers: {
        toggleSidebar(state, action: PayloadAction<boolean>) {
            state.isOpen = action.payload;
        },
        setActiveSection(state, action: PayloadAction<string>) {
            state.activeSection = action.payload;
        },
    },
});

export const { toggleSidebar, setActiveSection } = sidebarSlice.actions;

export default sidebarSlice.reducer;