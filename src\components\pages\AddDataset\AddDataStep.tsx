import React, { useEffect, useState } from "react";
import Image from "next/image";
import AddCompanyDataDetails from "./AddCompanyDataDetails";
import CheckboxDialog from "./CheckboxDialog";
import ProcessingDialog from "./ProcessingDialog";

interface FormData {
  visibility: string;
  title: string;
  description: string;
  tags: string[];
  purpose: string;
  source: string;
  license: string;
  departments: string[];
  projects: string[];
  datasets?: any[];
}

interface AddDataStepProps {
  formData: FormData;
  setFormData: (data: FormData) => void;
  setShowDetails: (showDetails: boolean) => void;
  showDetails: boolean;
  onComplete?: () => void; // Navigate to preview page
  scrollAtTopFunction: () => void;
}

const AddDataStep: React.FC<AddDataStepProps> = ({
  formData,
  setFormData,
  setShowDetails,
  showDetails,
  onComplete,
  scrollAtTopFunction,
}) => {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isProcessingOpen, setIsProcessingOpen] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleSaveCompanyData = (companyData: any) => {
    setFormData({
      ...formData,
      datasets:
        editingIndex !== null
          ? formData.datasets?.map((d, i) => (i === editingIndex ? companyData : d))
          : [...(formData.datasets || []), companyData],
    });

    setShowDetails(false);
    setEditingIndex(null);
  };

  const handleEdit = (index: number) => {
    setEditingIndex(index);
    setShowDetails(true);
  };

  const handleDelete = (index: number) => {
    setFormData({
      ...formData,
      datasets: formData.datasets?.filter((_, i) => i !== index),
    });
  };

  const handleFinalSave = () => {
    if (formData.datasets && formData.datasets.length > 0) {
      setIsDialogOpen(true);
    } else {
      // Proceed to the preview page if no datasets
      onComplete && onComplete();
    }
  };

  const handleDialogSave = (options: string[]) => {
    setSelectedOptions(options);
    setIsDialogOpen(false);

    // Show processing dialog
    setIsProcessingOpen(true);

    // Simulate API call
    setTimeout(() => {
      // Here you would call the create dataset API with the selected options
      // console.log("Creating dataset with options:", options);
      // console.log("Form data:", formData);

      // After processing is complete, you'd navigate to preview
      // We're leaving the dialog open - it will be closed by navigation
    }, 6000); // Allow time for animation to run through
  };

  const handleProcessingComplete = () => {
    // This would be called when ready to proceed to the next page
    setIsProcessingOpen(false);
    onComplete && onComplete();
  };

  const handleAddMoreData = () => {
    setShowDetails(true);
  };

  if (showDetails) {
    return (
      <AddCompanyDataDetails
        onSave={handleSaveCompanyData}
        initialData={editingIndex !== null ? formData.datasets?.[editingIndex] : undefined}
        formData={formData}
        scrollAtTopFunction={scrollAtTopFunction}
      />
    );
  }

  return (
    <div className="flex flex-col h-[80vh] w-full rounded-md p-6">
      {/* Display added datasets if any */}
      {formData.datasets && formData.datasets.length > 0 ? (
        <div className="w-full mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Added Data</h3>
            <button
              className="px-6 py-2 text-white bg-teal-500 rounded-md hover:bg-teal-600 flex items-center gap-2"
              onClick={handleAddMoreData}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                  clipRule="evenodd"
                />
              </svg>
              Add More Data
            </button>
          </div>
          <div className="space-y-4">
            {formData.datasets.map((dataset, index) => (
              <div key={index} className="p-4 border rounded-md flex justify-between items-center">
                <div>
                  <h4 className="font-medium text-gray-800">Uploaded File: {dataset.name}</h4>
                  <p className="text-gray-600">Description: {dataset.description}</p>
                  <p className="text-sm text-gray-500">Format: {dataset.format}</p>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleEdit(index)}
                    className="px-3 py-1.5 text-teal-500 hover:bg-teal-50 rounded flex items-center gap-1"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(index)}
                    className="px-3 py-1.5 text-gray-600 hover:bg-gray-100 rounded flex items-center gap-1"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fillRule="evenodd"
                        d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 flex justify-end">
            {/* <button
              className="px-6 py-2 text-white bg-teal-500 rounded-md hover:bg-teal-600"
              onClick={handleFinalSave}
            >
              Save
            </button> */}
          </div>
        </div>
      ) : (
        <div className="mb-4 flex flex-col items-center justify-center h-full">
          <Image src="/assets/add_dataset_icon.svg" alt="Add Data Icon" width={180} height={180} />
          <button
            className="px-6 py-2 text-white bg-teal-500 rounded-md hover:bg-teal-600 mt-4"
            onClick={() => setShowDetails(true)}
          >
            Start Adding Data
          </button>
        </div>
      )}

      {/* Checkbox Dialog for selecting processing options
      <CheckboxDialog 
        isOpen={isDialogOpen} 
        onClose={() => setIsDialogOpen(false)} 
        onSave={handleDialogSave} 
      />
      
      {/* Processing Dialog with changing text messages */}
      {/* <ProcessingDialog
        isOpen={isProcessingOpen}
        onClose={() => setIsProcessingOpen(false)}
        onComplete={handleProcessingComplete}
      /> */}
    </div>
  );
};

export default AddDataStep;
