"use client";
import Activity from "@/components/common/activity/activity";
import { Button } from "@/components/ui/button/button";
import { getDatasetHistory } from "@/service/history";
import { X } from "lucide-react";
import React, { useEffect, useState } from "react";

interface aboutIntercept {
    isOpen: boolean;
    setIsOpen: (isOpen: boolean) => void;
    title: string
    id: string
}

const ShowActivities: React.FC<aboutIntercept> = ({ isOpen, setIsOpen, title, id }) => {
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = "hidden"; // Prevent background scrolling
        } else {
            document.body.style.overflow = ""; // Restore background scrolling
        }
        return () => {
            document.body.style.overflow = ""; // Cleanup on unmount
        };
    }, [isOpen]);

    const [datasetHistoryResponse, setDatasetHistoryResponse] = useState<any>(null)

    useEffect(() => {
        const fetchHistory = async () => {
            const res: any = await getDatasetHistory(id);
            setDatasetHistoryResponse(res);
            // console.log("historddy", res);
        }
        fetchHistory()
    }, [])

    return (
        <div
            className={`z-50 fixed top-0 right-0 h-screen w-[calc(100%-420px)] bg-white shadow-lg transform ${isOpen ? "translate-x-0" : "translate-x-full"
                } transition-transform duration-300 ease-in-out flex flex-col`}
        >
            {/* Sticky Header */}
            <div className="sticky top-0 z-10 flex h-[48px] items-center justify-between bg-[#3B4154] px-6">
                <h2 className="text-lg font-medium text-white">Activities</h2>
                <Button
                    variant="ghost"
                    size="icon"
                    className="text-white hover:bg-white/20"
                    onClick={() => setIsOpen(false)}
                >
                    <X className="h-5 w-5" />
                </Button>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto p-5">
                <div className="flex gap-5">
                    <div className="flex flex-col flex-1 ml-5">
                        <div className="w-full">
                            <h1 className="font-bold text-xl text-[#3B4154] border-b mt-2">{title}</h1>
                            <div className="flex flex-col gap-5 mt-5">
                                {datasetHistoryResponse !== null && datasetHistoryResponse?.data.data.map((i: any, ind: number) => (<Activity total={datasetHistoryResponse?.data.data.length} index={ind} data={i} key={ind} />))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ShowActivities;
