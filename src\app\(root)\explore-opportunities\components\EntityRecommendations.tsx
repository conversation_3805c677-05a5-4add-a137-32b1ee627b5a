import React, { useState } from "react";
import ProgressBarTabs from "@/components/ui/progress-bar/ProgressBar";
import { ChevronDown, ChevronLeft, ChevronRight } from "lucide-react";
import AddAgentX from "@/components/common/addAgentx/AddAgentX";
import Tooltip from "@/components/ui/tooltip/Tooltip";

interface MatchedEntityAttribute {
  database: string | null;
  type: string;
  attribute: string;
  entity_attribute_description: string;
  reason: string;
  confidence_score: number;
  matched_dataset_attribute: string | null;
  dataset: string | null;
  isAdded?: boolean;
}

interface Entity {
  entity: string;
  entity_description: string;
  matched_entity_attributes: MatchedEntityAttribute[];
}

interface EntityRecommendationsProps {
  entities: Entity[];
  currentPage: number;
  totalPages: number;
  setCurrentPage: (page: number) => void;
  projectId?: string;
  setToast?: (toast: { message: string; type: "success" | "error" } | null) => void;
  getRecommendations?: (refresh: boolean) => void;
}

function formatString(input: string): string {
  if (!input) return "";
  return input
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}

const EntityRecommendations: React.FC<EntityRecommendationsProps> = ({
  entities,
  currentPage,
  totalPages,
  setCurrentPage,
  projectId,
  setToast,
  getRecommendations
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  if (!entities || entities.length === 0) {
    return (
      <div className="flex flex-row gap-52 items-center mt-5 p-5 border-[2px] border-teal-500 rounded-md">
        <h1 className="flex gap-2">
          <img src="/assets/icons/ai-avatar-icon.svg" alt="#" />
          <span className="font-bold text-xl">Recommendations</span>
        </h1>
        <div className="flex gap-1 justify-center text-sm">
          <span className="w-5 h-5">×</span>
          There are <b>No Recommendations</b> at this time
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mt-5 p-5 border-[2px] border-teal-500 rounded-md">
        <div className="flex justify-between items-center">
          <div className="flex flex-col w-1/2">
            <h1 className="flex gap-2">
              <img src="/assets/icons/ai-avatar-icon.svg" alt="#" />
              <span className="font-bold text-xl">
                {projectId ? "Recommendations" : "Data Readiness Score Review"}
              </span>
            </h1>
            <p className="text-sm">
              {projectId 
                ? "The recommended data sources below could increase value creation. Review these AI Avatar recommendations to unlock hidden insights and maximize project ROI."
                : "Review the data readiness scores and recommendations below to understand potential value creation opportunities."
              }
            </p>
          </div>
          <div className="w-1/2 flex gap-2 justify-end">
            <ProgressBarTabs
              currentStep={currentStep}
              setCurrentStep={setCurrentStep}
              totalSteps={entities.length}
              isOpen={isOpen}
            />
            <ChevronDown
              className={`w-6 h-6 cursor-pointer transition-transform duration-300 ${
                isOpen ? "rotate-180" : "rotate-0"
              }`}
              onClick={() => setIsOpen(!isOpen)}
            />
          </div>
        </div>

        <div
          className={`transition-all duration-500 overflow-hidden ${
            isOpen ? "opacity-100 mt-2" : "max-h-0 opacity-0"
          }`}
        >
          {currentStep < entities.length && (
            <div>
              <h1 className="text-lg font-semibold mt-3">
                {formatString(entities[currentStep]?.entity?.toString())}
              </h1>
              <p className="line-clamp-1">
                {entities[currentStep]?.entity_description}
              </p>
              <div className="grid grid-cols-2 gap-4 mt-5">
                {entities[currentStep]?.matched_entity_attributes?.map(
                  (item, index) => (
                    <div
                      key={`title-${item?.attribute}-${currentStep + index}`}
                      className="border-2 px-4 py-2 rounded-md border-gray-300"
                    >
                      <div className="flex flex-col">
                        <div className="flex justify-between items-center">
                          <h1 className="text-lg line-clamp-1">
                            {formatString(item?.attribute?.toString())}
                          </h1>
                          {projectId && (
                            <AddAgentX
                              projectId={projectId}
                              title={`${entities[currentStep]?.entity?.toString()}${" > "}${item.attribute?.toString()}`}
                              description={`${entities[currentStep]?.entity_description} ${item.entity_attribute_description}`}
                              tags={[
                                ...[
                                  formatString(entities[currentStep]?.entity?.toString()),
                                  item.type,
                                  [item?.database, item?.dataset, item?.matched_dataset_attribute]
                                    .filter(Boolean)
                                    .join(" > "),
                                ].filter((tag) => tag !== null && tag !== undefined && tag !== ""),
                              ]}
                              isAdd={item.dataset ? true : false}
                              isAddedtoProject={item.isAdded}
                              setToast={setToast}
                              getRecommendations={getRecommendations}
                            />
                          )}
                        </div>
                        {item.dataset ? (
                          <>
                            <p className="text-sm text-gray-500 line-clamp-1">
                              {item.entity_attribute_description}
                            </p>
                            <span className="flex flex-wrap gap-2 mt-1 mb-1">
                              <div className="flex items-center gap-1">
                                <span className="px-2 text-sm bg-gray-200 text-gray-700 rounded-md">
                                  {item.database?.split("_")?.join(" ")}
                                </span>
                                <span className="font-bold text-md">
                                  {item.dataset && (
                                    <ChevronRight className="w-5 h-5 font-semibold" />
                                  )}
                                </span>
                                <span className="px-2 text-sm bg-gray-200 text-gray-700 rounded-md">
                                  {item?.dataset}
                                </span>
                                <span className="font-bold text-md">
                                  {item.matched_dataset_attribute && (
                                    <ChevronRight className="w-5 h-5 font-semibold" />
                                  )}
                                </span>
                                <span className="px-2 text-sm bg-gray-200 text-gray-700 rounded-md">
                                  {item.matched_dataset_attribute}
                                </span>
                              </div>
                            </span>
                            <div className="flex flex-row gap-1 items-center mt-1">
                              <button className="px-2 border-2 border-teal-500 text-teal-500 rounded-md text-sm">
                                {item.confidence_score}% Readiness
                              </button>
                              <div className="flex items-center justify-center">
                                <Tooltip text="Relevance score for your projects" />
                              </div>
                            </div>
                          </>
                        ) : (
                          <span className="mt-1">
                            <button className="px-2 py-1 text-sm bg-gray-300 text-gray-700 rounded-md">
                              no data source found
                            </button>
                          </span>
                        )}
                      </div>
                      {!item.dataset && !projectId && (
                        <p className="text-sm mt-1">
                          {formatString(item.attribute?.toString())} data will benefit this project but we
                          couldn't find any source from your data.
                        </p>
                      )}
                      {!item.dataset && projectId && (
                        <p className="text-sm mt-1">
                          {formatString(item.attribute?.toString())} data will benefit this project but we
                          couldn't find any source from your data. If you can{" "}
                          <b>
                            upload some {formatString(item.attribute?.toString())} data assets.
                          </b>
                        </p>
                      )}
                    </div>
                  )
                )}
              </div>
            </div>
          )}
          {currentStep >= entities.length && (
            <div>
              <h1 className="text-lg font-semibold mt-3">Summary</h1>
              <div className="grid grid-cols-2 gap-4 mt-5">
                {entities?.map((item, index) => (
                  <div
                    key={`${item.entity}${index}`}
                    className="flex gap-5 items-center border-2 px-4 py-2 rounded-md border-gray-300"
                  >
                    <h1 className="text-lg line-clamp-1">
                      {formatString(item.entity?.toString())}
                    </h1>
                    <div>
                      <button className="px-2 py-1 text-sm bg-gray-300 text-gray-700 rounded-md">
                        {item.matched_entity_attributes.filter((attr) => attr.isAdded).length} data source added
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EntityRecommendations; 