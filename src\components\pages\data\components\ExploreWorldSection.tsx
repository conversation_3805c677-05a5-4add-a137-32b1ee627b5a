"use client"

import { Search } from "lucide-react"
import { Badge } from "@/components/ui/badge/badge"
import { But<PERSON> } from "@/components/ui/button/button"
import Input from "@/components/ui/search-input/search-input"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"

type DataAssetItemProps = {
  title: string
  description: string
  format?: string
  formats?: string[]
}

function DataAssetItem({ title, description, format, formats }: DataAssetItemProps) {
  return (
    <div className="border border-[#e4e4e4] rounded-lg p-4">
      <div className="flex justify-between items-start mb-2">
        <h4 className="font-medium text-[#333333] flex-1 pr-4">{title}</h4>
        <Button size="sm" className="bg-[#00b2a1] hover:bg-[#018e42] text-white">
          Import
        </Button>
      </div>

      <p className="text-sm text-[#666666] mb-3">{description}</p>

      <div className="flex gap-2">
        {format && (
          <Badge variant="secondary" className="bg-[#f0f2f8] text-[#666666]">
            {format}
          </Badge>
        )}
        {formats &&
          formats.map((fmt) => (
            <Badge key={fmt} variant="secondary" className="bg-[#f0f2f8] text-[#666666]">
              {fmt}
            </Badge>
          ))}
      </div>
    </div>
  )
}

export default function ExploreWorldSection() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="w-5 h-5" />
          Explore World Data
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#888888] w-4 h-4" />
          <Input placeholder="Search for Data Assets" className="pl-10 bg-[#f0f2f8] border-[#cfd2de]" />
        </div>

        <div className="space-y-4">
          <h4 className="font-medium text-[#333333]">Suggested Data Assets</h4>

          <DataAssetItem
            title="FixAuto Locations in USA"
            description="The dataset features key details such as store numbers, names, geographical coordinates, addresses, contact information, operational hours and status. This comprehensive..."
            format="xlsx"
          />

          <DataAssetItem
            title="Unfilled Orders in Manufacturing Excluding Defense, Not Seasonally Adjusted..."
            description="This dataset offers an in-depth analysis of work in process inventories in the total manufacturing sector from 1992 to 2024, not seasonally adjusted, and presented in millions..."
            formats={["xlsx", "csv"]}
          />
        </div>
      </CardContent>
    </Card>
  )
}
