"use client";
import React, { useEffect, useState } from "react";
import { useParams, useSearchParams } from "next/navigation";
import About from "@/components/pages/about/about";
import PreviewPage from "@/components/pages/PreviewPage/PreviewPage";
import { dataService } from "@/service/dataService";
import downloadData from "@/service/download-data";
import ShowActivities from "@/components/pages/show-activities/show-activities";
import Loader from "@/components/common/loader/loader";
import departmentService from "@/service/departments";
import AddToProject from "@/components/common/addToProject/addToProject";
import AdditionalFields from "./AdditionalFields";
import EditDataset from "@/components/pages/EditDataset/EditDataset";
import { toast } from "react-toastify";

interface DatasetByIdData {
  dataset: {
    updatedAt: string;
    createdAt: string;
    source: string;
    slug: string;
    title: string;
    description: string;
    tags: string[];
    files: {
      description: string;
      url: string;
      filename: string;
    }[];
    department?: string[];
    license?: string;
    dataplaceResponse: any;
    isImported: boolean;
  };
}

interface PageProps {
  isProject?: boolean;
  isCompanyData?: boolean;
}

const Page = ({ isProject, isCompanyData }: PageProps) => {
  const params = useParams();
  const datasetIdParam = params.datasetId ?? params.id;
  
  // Ensure it's a string (not an array or undefined)
  const datasetId: string | null = Array.isArray(datasetIdParam)
    ? datasetIdParam[0]
    : datasetIdParam ?? "";
  
  const searchParams = useSearchParams();
  const projectId = searchParams.get("projectId")?.toString();
  const isInProject = searchParams.get("isInProject")?.toString();

  const [data, setData] = useState<DatasetByIdData>({} as DatasetByIdData);
  const [tags, setTags] = useState<string[]>([]);
  const [filename, setFilename] = useState<string>("");
  const [isOpen, setIsOpen] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isShowActivitesOpen, setIsShowActivitesOpen] = useState(false);
  const [department, setDepartment] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [license, setLicense] = useState<string[]>([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [fileDescription, setFileDescription] = useState("");

  const fetchDatasetById = async () => {
    try {
      setLoading(true);
      const res = await dataService.getDatasetById(datasetId || "");
      setData(res.data);

      // Set tags and license
      if (res.data.dataset.tags) {
        setTags(res.data.dataset.tags);
      }
      if (res.data.dataset.license) {
        setLicense(res.data.dataset.license.split(","));
      }
      if (res.data.dataset.dataplaceResponse?.tags) {
        setTags((prevTags) => [...prevTags, ...res.data.dataset.dataplaceResponse.tags]);
      }

      // Build comma separated department names from the department array
      const deptIds = res.data.dataset.department || [];
      let deptNames: string[] = [];
      for (let i = 0; i < deptIds.length; i++) {
        const deptId = deptIds[i];
        const deptRes = await departmentService.getDepartmentById(deptId);
        const d = deptRes?.data?.department || {};
        if (d.name) {
          deptNames.push(d.name);
        }
      }
      const commaSeparatedNames = deptNames.join(", ");

      // Set default thumbnail and department based on data type
      let defaultThumbnail = "/assets/Group%204355.svg";
      if (isCompanyData) {
        const userLogo = process.env.NEXT_PUBLIC_USER_LOGO;
        if (userLogo) {
          defaultThumbnail = userLogo;
        }
        setDepartment({
          name: process.env.NEXT_PUBLIC_USER_NAME + " Data" || "",
          description: process.env.NEXT_PUBLIC_USER_DESCRIPTION || "",
          thumbnailUrl: defaultThumbnail,
          deptList: commaSeparatedNames,
          departmentIds: deptIds,
        });
      } else {
        // For world data, use static name and description
        setDepartment({
          name: "Sutra's World Data",
          description:
            "Your one-stop solution for accessing an extensive range of publicly available datasets. Our platform streamlines data discovery and acquisition, offering curated datasets across.",
          thumbnailUrl: defaultThumbnail,
          deptList: commaSeparatedNames,
          departmentIds: deptIds,
        });
      }
    } catch (e) {
      console.error("Error while previewing dataset:", e);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDatasetById();
  }, [datasetId, isCompanyData]);

  async function handleDownloadClick(filename: string) {
    try {
      const extension = filename.split(".").pop() ?? "";
      const mimeTypes: Record<string, string> = {
        pdf: "application/pdf",
        csv: "text/csv",
        xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        txt: "text/plain",
      };
      const contentType = mimeTypes[extension] ?? "application/octet-stream";
      const res = await downloadData(`${data.dataset.slug}/${filename}`);
      const blob = new Blob([res?.data], { type: contentType });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading file:", error);
    }
  }

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  if (loading || !data?.dataset) {
    return <Loader />;
  }

  const boldPlaceholder = (text: string) => <strong>{text}</strong>;
  const datasetDescription = data.dataset.description
    ? data.dataset.description
    : boldPlaceholder("No description found");

  const tagsContent =
    tags.length > 0 ? (
      tags.map((tag, index) => (
        <span key={index} className="px-[10px] py-[5px] text-[12px] bg-[#e9ecef] text-[#495057] rounded-[4px]">
          {tag}
        </span>
      ))
    ) : (
      <span>{boldPlaceholder("No tags found")}</span>
    );

  const licenseContent = license.length > 0 ? license.join(", ") : boldPlaceholder("No license found");

  const departmentName = department.name ? department.name : boldPlaceholder("No name found");
  const departmentDescription = department.description
    ? department.description
    : boldPlaceholder("No description found");
  const deptListContent =
    department.deptList && department.deptList.length > 0
      ? department.deptList
      : boldPlaceholder("No departments found");
  const timeAgo = (date: Date) => {
    const now = new Date();
    const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    const intervals: { [key: string]: number } = {
      year: 31536000,
      month: 2592000,
      week: 604800,
      day: 86400,
      hour: 3600,
      minute: 60,
      second: 1,
    };

    for (const interval in intervals) {
      const value = Math.floor(seconds / intervals[interval]);
      if (value >= 1) {
        return `${value} ${interval}${value > 1 ? "s" : ""} ago`;
      }
    }

    return "just now";
  };
  return (
    <div className="p-8" style={{ fontFamily: "var(--Helvetica-font-family)" }}>
      <header className="flex justify-between items-center border-b mt-2">
        <div className="flex gap-1 items-center mb-3 flex-grow pr-2">
          <h1 className="text-[24px] font-semibold text-[#3B4154] mr-2 flex-wrap max-w-full"> {data.dataset.title || "Untitled Dataset"}</h1>
          {data.dataset.isImported && (
            <button
              className="flex bg-[#F4F5F6] items-center gap-2 rounded-md px-2 py-1 
                        text-[#3B4154]"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="size-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m19.5 4.5-15 15m0 0h11.25m-11.25 0V8.25"
                />
              </svg>
              <div className="flex gap-1">
                <span className="text-md">Imported</span>
                <span className="text-md">Data</span>
                <span className="text-md">(World)</span>
              </div>
            </button>
          )}
        </div>
        <div className="flex gap-2 items-center text-sm mb-2 flex-shrink-0">
          <p className="text-[#888FAA] text-[16px] font-[400] whitespace-nowrap">
            Last Updated: {timeAgo(new Date(data.dataset.updatedAt))}
          </p>
          <button
            onClick={() => setIsShowActivitesOpen(true)}
            className="text-[#00B2A1] text-[15px] ml-[3px] whitespace-nowrap"
          >
            Show activities
          </button>
          <button
            className="flex items-center gap-1 px-4 py-2 text-teal-500 border border-teal-500 rounded hover:bg-teal-50 whitespace-nowrap"
            onClick={() => setIsEditMode(true)}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"
              />
              <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Manage
          </button>
          {isProject && projectId && isInProject && (
            <AddToProject projectId={projectId} datasetId={datasetId} isInProject={isInProject === "true"} />
          )}
        </div>
      </header>

      <div className="flex gap-4">
        <div className="flex flex-col flex-3/4" style={{ minWidth: "720px", width: "-webkit-fill-available" }}>
          <div className="mt-5 text-[#3B4154] flex flex-col gap-2">
            <h1 className="text-[20px] text-[#3B4154] font-semibold">Overview</h1>
            <p className="text-[16px] text-[#3B4154]">{datasetDescription}</p>
          </div>
          <div className="mt-5 text-[#3B4154] flex flex-col gap-2">
            <h1 className="text-[20px] text-[#3B4154] font-semibold">Tags</h1>
            <div>
              <div className="flex justify-between items-center">
                <span className="flex flex-wrap gap-2">{tagsContent}</span>
              </div>
            </div>
          </div>
          <div className="mt-8 text-[#3B4154] flex flex-col gap-2">
            <h1 className="text-[20px] text-[#3B4154] font-semibold">Data and Resources</h1>
            <div className="flex flex-col gap-2 mt-2">
              {data.dataset.files.map((file, index) => (
                <div key={index} className="flex justify-between items-center p-2 border border-[#CFD2DE] rounded-sm">
                  <div>
                    <div className="flex gap-[6px] items-center">
                      <img src="/assets/icons/description.svg" alt="" className="h-[17px] w-[17px]" />
                      <span className="text-sm text-[#333333]"> {file.filename}</span>
                    </div>
                  </div>
                  <div className="flex gap-5 text-[14px]">
                    <button
                      className="text-teal-500 flex gap-2 items-center"
                      onClick={() => {
                        setFilename(file.filename);
                        setFileDescription(file.description);
                        setIsPreviewOpen(true);
                      }}
                    >
                      <img src="/assets/icons/preview.svg" alt="" className="h-[17px]" />
                      Preview
                    </button>
                    <button
                      className="text-teal-500 flex gap-2 items-center"
                      onClick={() => handleDownloadClick(file.filename)}
                    >
                      <img src="/assets/icons/download.svg" alt="" className="h-[17px]" />
                      Download
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="mt-8 text-[#3B4154] flex flex-col gap-2">
            <h1 className="text-[20px] text-[#3B4154] pb-[10px] font-semibold">Additional Information</h1>
            <div className="flex items-start text-[#333333] text-[14px]">
              <div className="flex flex-2 flex-col w-[200px] gap-4">
                <p className=" pb-2 border-b-2 border-[#ccc] font-semibold">Fields</p>
                <p className="font-semibold">Source</p>
                <p className="font-semibold">State</p>
                <p className="font-semibold">Last Updated</p>
                <p className="font-semibold">Created On</p>
              </div>
              <div className="flex flex-[4] flex-col gap-4 ">
                <p className=" pb-2 border-b-2 border-[#ccc] font-semibold">Values</p>
                <p>{data.dataset.source !== "" ? data.dataset.source : "No source"}</p>
                <p>active</p>
                <p>
                  {new Date(data.dataset.updatedAt).toLocaleDateString("en-US", {
                    month: "long",
                    day: "numeric",
                    year: "numeric",
                  })}
                </p>
                <p>
                  {new Date(data.dataset.createdAt).toLocaleDateString("en-US", {
                    month: "long",
                    day: "numeric",
                    year: "numeric",
                  })}
                </p>
              </div>
            </div>
          </div>
          <div className="mt-8"></div>
          {data.dataset?.dataplaceResponse && <AdditionalFields dataplaceResponse={data.dataset.dataplaceResponse} />}
        </div>
        <div className="max-w-[450px] w-[23rem] h-fit flex flex-1/4 flex-col items-center bg-[#F4F5F6] mt-5 rounded-md p-2">
          <div className="bg-white w-full rounded-md mx-2 flex justify-center p-5">
            <img
              src={department.thumbnailUrl}
              alt="#"
              className="w-auto h-20"
              onError={(e) => {
                e.currentTarget.src = "/assets/Group%204355.svg";
              }}
            />
          </div>
          <div className="p-2 w-full">
            <h1 className="text-[18px] text-[#333333] font-semibold">{departmentName}</h1>
            <p className="mt-1 text-[#333333] text-[14px]">{departmentDescription}</p>
            {department.deptList && department.deptList.length > 0 && (
              <>
                <h1 className="text-[16px] text-[#333333] font-semibold mt-3">Department</h1>
                <p className="mt-1 text-[#333333] text-[14px]">{department.deptList}</p>
              </>
            )}
            <h1 className="text-[16px] text-[#333333] font-semibold mt-3">License</h1>
            <p className="mt-1 text-[#333333]  text-[14px]">{licenseContent}</p>
            <div className="flex justify-center mt-2">
              <button
                className="mt-4 bg-teal-500 rounded-[4px] text-[#FFFFFF] text-[14px] px-3 py-2"
                onClick={() => setIsOpen(true)}
              >
                Learn More
              </button>
              {(isShowActivitesOpen || isOpen || isPreviewOpen) && (
                <div
                  className="z-50 fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                  onClick={() => {
                    setIsShowActivitesOpen(false);
                    setIsOpen(false);
                    setIsPreviewOpen(false);
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </div>
      <div>
        <About isOpen={isOpen} setIsOpen={setIsOpen} department={department} />
        {filename !== "" && (
          <PreviewPage
            description={fileDescription}
            filename={filename}
            datasetId={data.dataset.slug}
            isOpen={isPreviewOpen}
            setIsOpen={setIsPreviewOpen}
            dataset={data.dataset}
          />
        )}
        <ShowActivities
          id={datasetId}
          title={data.dataset.title}
          isOpen={isShowActivitesOpen}
          setIsOpen={setIsShowActivitesOpen}

        />
        {isEditMode && (
          <>
            <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={() => setIsEditMode(false)} />
            <EditDataset
              isOpen={isEditMode}
              setIsOpen={setIsEditMode}
              datasetId={datasetId}
              initialData={data.dataset}
              onUpdate={() => {
                fetchDatasetById();
                setIsEditMode(false);
                toast.success("Dataset updated successfully!");
              }}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default Page;
