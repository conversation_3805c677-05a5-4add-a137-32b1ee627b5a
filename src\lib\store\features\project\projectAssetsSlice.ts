import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const assetsTabs = ["All Assets", "Project Assets"];

interface TabState {
  activeAssetTab: string;
}

// Create a slice for tab management
const tabSlice = createSlice({
  name: "assetsTab",
  initialState: {
    activeAssetTab: "All Assets",
  } as TabState,
  reducers: {
    setActiveAssetTab: (state, action: PayloadAction<string>) => {
      if (assetsTabs.includes(action.payload)) {
        state.activeAssetTab = action.payload;
      }
    },
  },
});

// Export actions
export const { setActiveAssetTab } = tabSlice.actions;
export default tabSlice.reducer;
