"use client";
import { setImportActiveTab } from "@/lib/store/features/dataset/importSlice";
import { dataService } from "@/service/dataService";
import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { useSearchParams } from "next/navigation";

interface TileProps {
  title: string;
  description?: string;
  tags?: string[];
  button_text?: string;
  onButtonClick: () => void;
  id?: string;
  isImportedData?: boolean;
  setUrlQuery?: React.Dispatch<React.SetStateAction<any>>;
}
interface UrlQuery {
  page: number;
  limit: number;
  search: string;
  tags: string;
  license: string;
  fileTypes: string;
  sortBy: string;
  sortOrder: string;
}

const Tile: React.FC<TileProps> = ({
  title,
  description,
  tags,
  button_text,
  onButtonClick,
  id,
  isImportedData,
  setUrlQuery,
}) => {
  const [isImported, setIsImported] = useState(isImportedData);
  const [error, setError] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch();

  const handleImport = async (datasetId: string) => {
    try {
      setLoading(true);
      const res = await dataService.importWorldData({
        datasetId: datasetId,
        orgId: "",
        token: "",
      });
      if (res.status == 200) {
        setError(false);
        setIsImported(true);
      } else {
        setError(true);
      }
    } catch (error) {
      setError(true);
      //setIsImported(false);
    } finally {
      setLoading(false);
    }
  };

  const handleImportClick = () => {
    setError(false);
    if (id) handleImport(id);
  };

  const handleClick = () => {
    onButtonClick();
  };

  const handleImportView = () => {
    if (setUrlQuery) {
      setUrlQuery((prevState: UrlQuery) => ({
        ...prevState,
        sortBy: "updatedAt",
        search: "",
      }));
    }
    router.push(`/data/world-data`);
    dispatch(setImportActiveTab("Imported Data"));
  };

  function formatTitleString(input: string): string {
    if (!input) return "";
    return input
      .split(" ")
      .map((wrd) =>
        wrd
          .split("_")
          .map((w) => w.charAt(0).toUpperCase() + w.slice(1).toLowerCase())
          .join(" ")
      )
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ")
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  }

  return (
    <div className="flex justify-between items-center border-b-2 py-2 pb-6 font-[Lato] gap-2">
      <div className={`${button_text ? "w-3/4" : "w-full"} pr-4`}>
        <div className="w-full pr-4">
          <h2 className="text-[18px] text-[#3B4154] font-semibold">
            {formatTitleString(title)}
          </h2>
          {description && (
            <p className="mt-2 text-slate-500 line-clamp-2">{description}</p>
          )}
          {tags && tags.length > 0 && (
            <div className="mt-4 flex flex-wrap gap-2">
              {tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-3 py-1 text-sm bg-gray-300 text-gray-700 rounded-md"
                >
                  {tag}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
      {button_text && (
        <div className="w-1/4 flex flex-col items-end gap-1">
          {!isImported && button_text == "Import" && (
            <div className="flex flex-col ">
              <span className="flex justify-end">
                <button
                  className="flex items-center space-x-2 rounded-[3px] px-4 py-2 bg-teal-500 text-white text-sm"
                  onClick={handleImportClick}
                >
                  {loading ? (
                    <div className="px-6">
                      <Loader2 className="w-4 h-4 animate-spin" />
                    </div>
                  ) : (
                    <>
                      {button_text === "Import" && (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="w-4 h-4"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="m19.5 4.5-15 15m0 0h11.25m-11.25 0V8.25"
                          />
                        </svg>
                      )}
                      <span>{button_text}</span>
                    </>
                  )}
                </button>
              </span>
              {error && (
                <div className="text-red-500 mt-2">
                  Failed to import dataset
                </div>
              )}
            </div>
          )}
          {button_text && button_text !== "Import" && (
            <button
              className="flex items-center space-x-2 rounded-[3px] px-4 py-2 bg-teal-500 text-white text-sm"
              onClick={handleClick}
            >
              <span>{button_text}</span>
            </button>
          )}
          {isImported && (
            <div className="flex flex-col items-end">
              <button
                disabled
                className="bg-slate-300 text-slate-400 rounded-[3px] px-4 py-2 cursor-not-allowed text-sm"
              >
                Imported
              </button>
              {!isImportedData && (
                <div className="flex items-center text-sm mt-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-4 h-4 mr-2 text-teal-500"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M9 12.75l2.25 2.25L15 9.75M21 12c0 
                     4.971-4.029 9-9 9s-9-4.029-9-9 4.029-9 
                     9-9 9 4.029 9 9z"
                    />
                  </svg>
                  <span className="text-black whitespace-nowrap">
                    Dataset imported.
                  </span>
                  <button
                    className="text-teal-500 ml-2 whitespace-nowrap cursor-pointer"
                    onClick={handleImportView}
                  >
                    Click here to view.
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Tile;
