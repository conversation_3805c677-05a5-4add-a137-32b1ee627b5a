import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { X } from "lucide-react";
import { But<PERSON> } from "@/components/pages/project/Comments/comments-component/comment-UI/button";
import { CommentInput } from "./input";
import { CommentList } from "./list";
import type { Comment } from "@/components/pages/project/Comments/comments-types";
import { ProjectDetails } from "@/service/types/types";
import { createProjectComment, deleteProjectComment, getProjectComment } from "@/service/projects";
import { setCommentCount } from "@/lib/store/features/project/projectCommentSlice";

interface CommentPanelProps {
  isOpen: boolean;
  onClose: () => void;
  projectData: ProjectDetails | null;
}

function CommentPanel({ isOpen, onClose, projectData }: CommentPanelProps) {
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const dispatch = useDispatch();
  const panelRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (isOpen) {
      fetchComments();
    }
  }, [isOpen]);

  const fetchComments = async () => {
    setIsLoading(true);
    try {
      const response = await getProjectComment(projectData?._id || "");
      dispatch(setCommentCount({ commentCount: response.data.comments.length || 0 }));
      setComments(response.data.comments);
      // console.log(response.data.comments.length);
    } catch (error) {
      console.error("Failed to fetch comments:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (comment: string) => {
    try {
      const response = await createProjectComment(projectData?._id || "", comment || "");
      if (response.status === 200 || response.status === 201) {
        await fetchComments();
      }
    } catch (error) {
      console.error("Failed to submit comment:", error);
    }
  };

  const handleDelete = async (commentId: string) => {
    // console.log("this is called");
    try {
      const response = await deleteProjectComment(commentId);
      if (response.status === 200 || response.status === 201) {
        await fetchComments();
      }
    } catch (error) {
      console.error("Failed to delete comment:", error);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (panelRef.current && !panelRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  return (
    <>
      <div
        className={`fixed inset-0 bg-black/50 transition-opacity ${isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
          }`}
        onClick={onClose}
        style={{ zIndex: 40 }}
      />
      <div
        ref={panelRef}
        className={`fixed top-0 right-0 w-full md:w-[45%] h-full bg-background shadow-lg transform transition-transform duration-300 ${isOpen ? "translate-x-0" : "translate-x-full"
          }`}
        style={{ zIndex: 50 }}
      >
        <div className="flex items-center justify-between px-2 h-12 bg-[#3b4154] text-white" style={{ zIndex: 60 }}>
          <h2 className="text-lg px-[12px]">Comments</h2>
          <button className="text-white hover:text-gray-300" onClick={onClose}>
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="h-[calc(100vh-3.5rem)] overflow-y-auto mt-3">
          <CommentInput onSubmit={handleSubmit} />
          <CommentList comments={comments} projectData={projectData} isLoading={isLoading} onDelete={handleDelete} />
        </div>
      </div>
    </>
  );
}

export { CommentPanel };
