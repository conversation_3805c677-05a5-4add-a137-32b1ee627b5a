"use client";

import { usePathname } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import Breadcrumb from "../breadcrumb/Breadcrumb";

export default function MainContent({ children }: { children: React.ReactNode }) {
  const isOpen = useSelector((state: any) => state.sidebar.isOpen);
  const pathname = usePathname();
  const mainRef = useRef<HTMLElement | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    if (mainRef.current) {
      mainRef.current.scrollTo(0, 0);
    }
  }, [pathname]);

  useEffect(() => {
    // Function to check if viewport is mobile
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768); // 768px is a common breakpoint for mobile
    };

    // Check on initial load
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener("resize", checkIfMobile);

    // Cleanup event listener on component unmount
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  return (
    <main
      ref={mainRef}
      style={{ height: "calc(100vh - 4rem)", overflowY: "auto" }}
      className={`flex-1 transition-all duration-300 rounded-tl-xl shadow-lg bg-white ${
        // sidebar width (w-53)
        isMobile ? "ml-0" : (isOpen ? "ml-[320px]" : "ml-[160px]")
        }`}
    >
      {/* Only show Breadcrumb on desktop */}
      {!isMobile && <Breadcrumb />}
      {children}
    </main>
  );
}
