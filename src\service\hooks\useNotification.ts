"use client";

import { useState, useEffect, useCallback } from "react";
import { io, type Socket } from "socket.io-client";
import {
  getNotifications,
  getNotificationsCount,
  markAllNotificationRead,
  markNotificationRead,
} from "../notification";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";

interface Notification {
  _id: string;
  title: string;
  message: string;
  type: string;
  relatedId?: string;
  relatedType?: string;
  relatedName?: string;
  metadata?: Record<string, any>;
  isRead: boolean;
  createdAt: string;
  readAt?: string;
}

let socket: Socket | null = null;

export const useNotifications = (limit = 20) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const token = useSelector((state: RootState) => state.user.token);
  const isAuthenticated = useSelector(
    (state: RootState) => state.user.isAuthenticated
  );

  const fetchNotifications = useCallback(
    async (page = 1, replace = true) => {
      try {
        setLoading(true);
        const response = await getNotifications({ page, limit });
        const { notifications: fetchedNotifications, pagination } = response;
        setNotifications((prev) =>
          replace ? fetchedNotifications : [...prev, ...fetchedNotifications]
        );
        setHasMore(pagination.page < pagination.totalPages);
        setError(null);
      } catch (err) {
        setError("Failed to fetch notifications");
        console.error("Error fetching notifications:", err);
      } finally {
        setLoading(false);
      }
    },
    [limit]
  );

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchNotifications(nextPage, false);
    }
  }, [fetchNotifications, hasMore, loading, page]);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const res = await markNotificationRead(notificationId);
      setNotifications((prev) =>
        prev.map((notification) =>
          notification._id === notificationId
            ? { ...notification, isRead: true }
            : notification
        )
      );
    } catch (err) {
      console.error("Error marking notification as read:", err);
    }
  }, []);

  const markAllAsRead = useCallback(
    async (
      setCount: React.Dispatch<React.SetStateAction<number>> = () => {}
    ) => {
      try {
        const res = await markAllNotificationRead();
        if (setCount) {
          setCount(0);
        }
        setNotifications((prev) =>
          prev.map((notification) => ({ ...notification, isRead: true }))
        );
        // No need to emit via socket as the server will handle that
      } catch (err) {
        console.error("Error marking all notifications as read:", err);
      }
    },
    []
  );

  // Initialize socket connection and fetch notifications
  useEffect(() => {
    if (!token) {
      setError("Authentication token not found");
      setLoading(false);
      return;
    }

    // Initialize socket if not already connected
    if (!socket) {
      socket = io(
        process.env.NEXT_PUBLIC_SOCKET_BASE_URL || "http://18.234.247.164:5004",
        {
          auth: { token },
          transports: ["websocket"],
        }
      );

      socket.on("connect", () => {
        // console.log("Socket connected");
      });

      socket.on("connect_error", (err: any) => {
        console.error("Socket connection error:", err);
        setError("Failed to connect to notification service");
      });
    }

    // Fetch initial notifications
    fetchNotifications();

    // Socket event handlers
    const handleNewNotification = (notification: Notification) => {
      setNotifications((prev) => [notification, ...prev]);
    };

    const handleNotificationRead = (notificationId: string) => {
      setNotifications((prev) =>
        prev.map((notification) =>
          notification._id === notificationId
            ? { ...notification, isRead: true }
            : notification
        )
      );
    };

    const handleAllNotificationsRead = () => {
      setNotifications((prev) =>
        prev.map((notification) => ({ ...notification, isRead: true }))
      );
    };

    const handleNotificationDeleted = (notificationId: string) => {
      setNotifications((prev) =>
        prev.filter((notification) => notification._id !== notificationId)
      );
    };

    // Register socket event listeners
    socket.on("notification:new", handleNewNotification);
    socket.on("notification:read", handleNotificationRead);
    socket.on("notification:all-read", handleAllNotificationsRead);
    socket.on("notification:deleted", handleNotificationDeleted);

    // Cleanup function
    return () => {
      if (socket) {
        socket.off("notification:new", handleNewNotification);
        socket.off("notification:read", handleNotificationRead);
        socket.off("notification:all-read", handleAllNotificationsRead);
        socket.off("notification:deleted", handleNotificationDeleted);
      }
    };
  }, [fetchNotifications]);

  return {
    notifications,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    hasMore,
    loadMore,
  };
};

export const useNotificationCount = () => {
  const token = useSelector((state: RootState) => state.user.token);
  const isAuthenticated = useSelector(
    (state: RootState) => state.user.isAuthenticated
  );
  const [count, setCount] = useState(0);
  useEffect(() => {
    if (!token) {
      return;
    }

    // Initialize socket if not already connected
    if (!socket) {
      socket = io(
        process.env.NEXT_PUBLIC_SOCKET_BASE_URL || "http://18.234.247.164:5004",
        {
          auth: { token },
          transports: ["websocket"],
        }
      );
    }

    // Fetch initial count
    const fetchCount = async () => {
      try {
        const response = await getNotificationsCount();
        setCount(response.count);
      } catch (err) {
        console.error("Error fetching notification count:", err);
      }
    };

    fetchCount();

    // Socket event handlers
    const handleCountUpdate = (newCount: number) => {
      setCount(newCount);
    };

    const handleNewNotification = () => {
      setCount((prev) => prev + 1);
    };

    const handleNotificationRead = () => {
      setCount((prev) => Math.max(0, prev - 1));
    };

    const handleAllNotificationsRead = () => {
      setCount(0);
    };

    // Register socket event listeners
    socket.on("notification:count", handleCountUpdate);
    socket.on("notification:new", handleNewNotification);
    socket.on("notification:read", handleNotificationRead);
    socket.on("notification:all-read", handleAllNotificationsRead);

    // Cleanup function
    return () => {
      if (socket) {
        socket.off("notification:count", handleCountUpdate);
        socket.off("notification:new", handleNewNotification);
        socket.off("notification:read", handleNotificationRead);
        socket.off("notification:all-read", handleAllNotificationsRead);
      }
    };
  }, []);

  return { count, setCount };
};
