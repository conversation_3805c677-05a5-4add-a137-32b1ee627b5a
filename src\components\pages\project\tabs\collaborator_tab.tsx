import React from "react";
import { useState, useEffect } from "react";
import ConfirmDialog from "@/components/common/collabDeleteDialog/collab_delete_dialog";
import { Collaborator, ProjectDetails, User } from "@/service/types/types";
import { FaTrash } from "react-icons/fa";
import SlideOutPanel from "@/components/common/CollabSideOut/collab_sideout";
import { Toast } from "@/components/ui/toast/toast";
import { MdPerson } from "react-icons/md";
import { getCollabrators, removeCollabrator } from "@/service/projects";
import { userService } from "@/service/api";
import Loader from "@/components/common/loader/loader";

type ToastState = {
  message: string;
  type: "success" | "error";
} | null;

const CollaboratorTab = ({
  projectDetails,
  collaborators,
  nonCollaborators,
  setToast,
  fetchCollaborators,
  fetchNonCollaborators,
  fetchProjectDetails,
}: {
  projectDetails: ProjectDetails | null;
  collaborators: Collaborator[] | null;
  nonCollaborators: User[] | null;
  setToast: React.Dispatch<React.SetStateAction<ToastState>>;
  fetchCollaborators: () => void;
  fetchNonCollaborators: () => void;
  fetchProjectDetails: () => void;
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [selectedCollaborator, setSelectedCollaborator] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const imgBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "";


  const handleRemoveClick = (id: string, name: string) => {
    setSelectedCollaborator({ id, name });
    setIsDialogOpen(true);
  };

  const handleConfirmRemove = async () => {
    if (selectedCollaborator && projectDetails) {
      try {
        setLoading(true);
        const res = await removeCollabrator(
          projectDetails?._id,
          selectedCollaborator?.id
        );
        if (res.status == 200) {
          setToast({
            message: "Collaborators removed successfully",
            type: "success",
          });
          fetchCollaborators();
          fetchNonCollaborators();
          fetchProjectDetails();
        } else {
          setToast({ message: res.data.message, type: "error" });
        }
      } catch (error) {
        setToast({ message: "Something went wrong!", type: "error" });
        // console.log(error);
      } finally {
        setLoading(false);
      }
    }
    setIsDialogOpen(false);
  };

  return (
    <div className="bg-white w-full font-[Lato]">
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl px-2 py-4">Collaborators</h2>
            {(projectDetails?.currentUserRole?.userRole == "Executive" ||
              projectDetails?.currentUserRole?.userRole == "Creator") && (
                <button
                  onClick={() => setIsPanelOpen(true)}
                  className="px-4 py-1 rounded flex items-center"
                  style={{
                    backgroundColor: "var(--sidebar-item-selected)",
                    color: "#fff",
                  }}
                >
                  Add Collaborator
                </button>
              )}
          </div>
          <hr />
          {collaborators?.length == 0 ||
            (collaborators?.length == 1 &&
              collaborators[0]?.userRole == "Creator") ? (
            <h1 className="text-xl text-gray-700 font-semibold p-5 mt-10 flex justify-center items-center">
              No Collaborators to show
            </h1>
          ) : (
            <>
              <div className="rounded-md overflow-hidden">
                <table className="w-full text-left">
                  <thead>
                    <tr className="">
                      <th className="py-2 px-4 font-normal">Name</th>
                      <th className="py-2 px-4 font-normal">Role</th>
                    </tr>
                  </thead>
                  <tbody>
                    {collaborators?.map(
                      (collaborator: Collaborator, index: number) => {
                        return (
                          collaborator?.userRole !== "Creator" && (
                            <tr key={index} className="border-t border-b">
                              <td className="py-2 px-4 flex items-center gap-3">
                                <div className="w-7 h-7 rounded-full bg-gray-500 flex items-center justify-center">
                                  {collaborator?.user.thumbnail ? (
                                    <img
                                      src={imgBaseUrl?.replace("/api", "") + collaborator.user.thumbnail}
                                      alt="User Avatar"
                                      className="w-full h-full rounded-full object-cover"
                                    />
                                  ) : (
                                    <MdPerson className="w-full h-full rounded-full object-cover" />
                                  )}
                                </div>

                                <div>
                                  <p className="text-sm">
                                    {collaborator.user.name}
                                  </p>
                                  <p className="text-sm text-gray-500">
                                    {collaborator.user.email}
                                  </p>
                                </div>
                              </td>
                              <td className="py-2 px-4 text-sm">
                                {collaborator?.userRole}
                              </td>
                              <td className="py-2 px-4 flex justify-end">
                                {(projectDetails?.currentUserRole?.userRole ==
                                  "Executive" ||
                                  projectDetails?.currentUserRole?.userId ==
                                  projectDetails?.creator._id) &&
                                  projectDetails?.currentUserRole?.userId !==
                                  collaborator?.user?._id && (
                                    <button
                                      onClick={() =>
                                        handleRemoveClick(
                                          collaborator.user._id,
                                          collaborator.user.name
                                        )
                                      }
                                      className="text-[#C0433C] text-sm flex items-center gap-1"
                                    >
                                      <FaTrash size={12} />
                                      Remove
                                    </button>
                                  )}
                              </td>
                            </tr>
                          )
                        );
                      }
                    )}
                  </tbody>
                </table>
              </div>
            </>
          )}
          {isPanelOpen && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 transition-opacity z-50"
              onClick={() => setIsPanelOpen(false)}
            />
          )}
          {/* Dialog */}
          <ConfirmDialog
            isOpen={isDialogOpen}
            onClose={() => setIsDialogOpen(false)}
            onConfirm={handleConfirmRemove}
            collaboratorName={selectedCollaborator?.name || ""}
          />
          {/* Slide-Out Panel */}
          <SlideOutPanel
            isOpen={isPanelOpen}
            onClose={() => setIsPanelOpen(false)}
            title="Add Collaborator"
            projectId={projectDetails?._id || ""}
            setToast={setToast}
            nonCollaborators={nonCollaborators}
            fetchCollaborators={fetchCollaborators}
            fetchNonCollaborators={fetchNonCollaborators}
            fetchProjectDetails={fetchProjectDetails}
          />
        </>
      )}
    </div>
  );
};

export default CollaboratorTab;
