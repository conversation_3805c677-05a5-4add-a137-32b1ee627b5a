interface InstanceDetails {
  endpoint: string;
  healthCheck: string;
}

interface Tag {
  _id: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface Issue {
  _id: string;
  title: string;
  description: string;
  status: string;
}

interface ChangeLog {
  _id: string;
  timestamp: string;
  message: string;
  level: string;
}

interface Configuration {
  inputSize: number;
  confidenceThreshold: number;
}

export interface AIBoxDetailsModel {
  instanceDetails: InstanceDetails;
  _id: string;
  name: string;
  version: string;
  description: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  repository: string;
  thumbnailUrl: string;
  tags: Tag[];
  dependencies: string[];
  configuration: Configuration;
  readme: string;
  issues: Issue[];
  changeLogs: ChangeLog[];
}

export interface User {
  _id: string;
  name: string;
  email: string;
  isDeleted: boolean;
  department: UserDepartment[];
  createdAt: string;
  thumbnail: string;
  updatedAt: string;
  __v: number;
  isSysAdmin?: boolean;
  password?: string;
}

export interface UserDepartment {
  departmentId: string;
  departmentRole: string;
}
