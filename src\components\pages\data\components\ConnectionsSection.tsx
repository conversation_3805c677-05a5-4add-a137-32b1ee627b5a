"use client"

import { Database } from "lucide-react"
import { Badge } from "@/components/ui/badge/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

type ConnectionItemProps = {
  name: string
  type: string
  category: string
  status: string
  statusColor: string
  critical?: boolean
}

function ConnectionItem({
  name,
  type,
  category,
  status,
  statusColor,
  critical = false,
}: ConnectionItemProps) {
  return (
    <div className="border border-[#e4e4e4] rounded-lg p-4">
      <div className="flex justify-between items-start">
        <div>
          <div className="flex items-center gap-2 mb-1">
            <h4 className="font-medium text-[#333333]">{name}</h4>
            {critical && <Badge className="bg-[#fee2e1] text-[#dc2625]">Critical</Badge>}
          </div>
          <p className="text-sm text-[#666666]">{type}</p>
          <p className={`text-sm ${critical ? "text-[#dc2625]" : "text-[#666666]"}`}>{category}</p>
        </div>
        <Badge
          className={`${
            statusColor === "text-[#018e42]" ? "bg-[#dcfce7] text-[#018e42]" : "bg-[#fee2e1] text-[#dc2625]"
          }`}
        >
          {status}
        </Badge>
      </div>
    </div>
  )
}

export default function ConnectionsSection() {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5" />
          Connections
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <ConnectionItem
          name="TDIC"
          type="DBMS: Microsoft SQL Server"
          category="Category: General"
          status="Active"
          statusColor="text-[#018e42]"
        />

        <ConnectionItem
          name="TDIC_Forecast"
          type="DBMS: Microsoft SQL Server"
          category="Category: Analytics"
          status="Active"
          statusColor="text-[#018e42]"
        />

        <ConnectionItem
          name="SAP ERP - Production"
          type="Last Sync: 1 day ago"
          category="Authentication failed"
          status="Inactive"
          statusColor="text-[#dc2625]"
          critical
        />
      </CardContent>
    </Card>
  )
}
