import { http } from "./methods"

export const getDatasetHistory = async (id: string) => {
    const res = await http.get(`/datasets/${id}/history`);
    return res;
}

export const getProjectHistory = async (id: string) => {
    const res = await http.get(`/projects/${id}/history`);
    return res;
}

export const getDepartmentHistory = async (id: string) => {
    const res = await http.get(`/departments/history/${id}`);
    return res;
}

export const getDepartmentHistoryByDepartmentIds = async (departmentIds: string[], page: number, limit: number) => {
    const res : any = await http.get(`/departments/multi-department-history?${departmentIds.map(id => `departmentIds=${id}`).join('&')}&page=${page}&limit=${limit}`);
    return res;
}