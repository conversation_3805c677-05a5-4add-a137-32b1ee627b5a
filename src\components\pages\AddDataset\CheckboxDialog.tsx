import React from "react";
import Checkbox from "@/components/ui/checkbox/page";
import Image from "next/image";

interface CheckboxDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (selectedOptions: string[]) => void;
  selectedOptions: string[];
  setSelectedOptions: React.Dispatch<React.SetStateAction<string[]>>;
}

const CheckboxDialog: React.FC<CheckboxDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  selectedOptions,
  setSelectedOptions

}) => {
  // const [selectedOptions, setSelectedOptions] = React.useState<string[]>([
  //   "tags",
  //   "ontology",
  //   "unified_data_summary",
  //   "files_content_ontologies",
  //   "time_range",
  //   "geo_location",
  //   "language",
  //   "data_industry",
  //   "intended_use_case",
  //   "ai_ml_use_case",
  //   "file_content_summary",
  //   "validate_file",
  // ]);

  const toggleOption = (value: string) => {
    setSelectedOptions((prev) =>
      prev.includes(value)
        ? prev.filter((option) => option !== value)
        : [...prev, value]
    );
  };

  const handleSelectAll = (isChecked: boolean) => {
    if (isChecked) {
      setSelectedOptions([
        "tags",
        "ontology",
        "unified_data_summary",
        "files_content_ontologies",
        "time_range",
        "geo_location",
        "language",
        "data_industry",
        "intended_use_case",
        "ai_ml_use_case",
        "file_content_summary",
        "validate_file",
      ]);
    } else {
      setSelectedOptions([]);
    }
  };

  const handleSave = () => {
    onSave(selectedOptions);
  };

  if (!isOpen) return null;

  const isAllSelected = selectedOptions.length === 12;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg w-[550px] flex flex-col justify-between border border-gray-200">
        <div className="flex items-center mb-3">
          <Image src="/Group 4993.svg" alt="Dataplace Icon" width={20} height={10} />
          <h2 className="text-gray-800 text-sm font-semibold ml-2">
            The dataset will undergo processing using our AI-powered Dataplace technology.
          </h2>
        </div>
        <p className="text-gray-800 text-md font-semibold mb-2">
          Select data attributes to be processed by AI.
        </p>
        <div className="space-y-2 mb-4 text-sm">
          <div className="text-gray-800 flex items-center font-semibold">
            <Checkbox
              id="selectAll"
              label="Select All (Recommended)"
              checked={isAllSelected}
              onChange={(e:any) => handleSelectAll(e.target.checked)}
            />
          </div>
          {[
            { id: "tags", label: "Extract Search Tags" },
            { id: "ontology", label: "Extract the World Ontology" },
            { id: "unified_data_summary", label: "Generate a Unified Data Summary" },
            { id: "files_content_ontologies", label: "Generate File Content Ontologies" },
            { id: "time_range", label: "Extract the Time Range of Data" },
            { id: "geo_location", label: "Extract Geo Locations" },
            { id: "language", label: "Extract the Language of the Data" },
            { id: "data_industry", label: "Extract the Data Industry (Domain)" },
            { id: "intended_use_case", label: "Extract Intended Use Cases" },
            { id: "ai_ml_use_case", label: "Extract AI/ML Use Cases" },
            { id: "file_content_summary", label: "Generate the File Content Summary" },
            { id: "validate_file", label: "Validate the File Content with Text" },
          ].map(({ id, label }) => (
            <Checkbox
              key={id}
              id={id}
              label={label}
              checked={selectedOptions.includes(id)}
              onChange={() => toggleOption(id)}
            />
          ))}
        </div>
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-600 hover:bg-gray-50 text-sm"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-[#00B2A1] text-white rounded-md hover:bg-[#00A090] text-sm"
          >
            Process and Save
          </button>
        </div>
      </div>
    </div>
  );
};

export default CheckboxDialog;
