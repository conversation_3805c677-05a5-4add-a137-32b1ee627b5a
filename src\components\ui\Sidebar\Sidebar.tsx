"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useSidebar } from "@/context/sidebar-context";
import { NavItem } from "@/types";
import { useEffect, useState } from "react";
import { navItems } from "./nav-items";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/lib/store";
import { openIntercom } from "@/lib/store/features/intercom/intercomSlice";

export default function Sidebar() {
  const { isOpen } = useSidebar();
  const pathname = usePathname();
  const dispatch = useDispatch();
  const [activeKey, setActiveKey] = useState<string | null>(
    localStorage?.getItem("sidebar-active-item")
      ? JSON.parse(localStorage.getItem("sidebar-active-item") as string)
      : ""
  );

  const getInitialExpandedState = () => {
    const activeParent = navItems.find((item) =>
      item.children?.some(
        (child) => trimPath(pathname) === child.href || trimPath(pathname) === "/data"
      )
    );
    return activeParent ? { [activeParent.label]: true } : {};
  };

  const [expandedItems, setExpandedItems] = useState<{ [key: string]: boolean }>(
    getInitialExpandedState()
  );

  useEffect(() => {
    const newExpandedState = getInitialExpandedState();
    setExpandedItems(newExpandedState);
    localStorage.setItem("sidebar-expanded-items", JSON.stringify(newExpandedState));
  }, [pathname]);

  const toggleItem = (title: string) => {
    setExpandedItems((prev) => {
      const newState = { [title]: !prev[title] };
      return newState;
    });
  };

  const favorites = useSelector((state: RootState) => state.favorites.items);

  const [favItems, setFavItems] = useState<NavItem[]>([]);
  useEffect(() => {
    const mappedFavorites = favorites.map((fav: { name: any; href: any; id: any; }) => ({
      label: fav.name,
      href: fav.href,
      icon: "star",
      iconSelected: "star-selected",
      key: fav.id,
    }));
    setFavItems(mappedFavorites);
  }, [favorites]);

  return (
    <aside
      className={`fixed left-0 top-12 h-full bottom-6 bg-sidebarBackground text-[14px] text-white shadow-lg z-50 transition-transform duration-300 ${isOpen ? "w-[210px] translate-x-0" : "-translate-x-full"
        }`}
      style={{
        height: "calc(100vh - 4.5rem)",
        zIndex: 35,
      }}
    >
      <div className="mt-[32px] overflow-y-auto sidebar-scroll-container h-[calc(100%-2rem)] flex flex-col">
        <div className="flex-1 overflow-y-auto sidebar-scroll-container pb-2">
          <nav className="space-y-1">
            {navItems.map((item) => (
              <NavLink
                key={item.href}
                item={item}
                pathname={pathname}
                isSidebarOpen={isOpen}
                expandedItems={expandedItems}
                toggleItem={toggleItem}
                activekey={activeKey}
                setActiveKey={setActiveKey}
              />
            ))}
            <button className="w-full flex items-center gap-3 px-3 py-3 transition-colors hover:bg-sidebarBackgroundHover" onClick={() => dispatch(openIntercom())}>
              <img src="/assets/icons/support.svg" alt="Support" className="w-5 h-5" />
              Support
            </button>
          </nav>
        </div>

        {/* Favorites section */}
        <div className="border-t-[0.5px] border-sidebarBorder pt-2">
          <div className="text-[10px] font-medium uppercase tracking-wider text-gray-400 px-3 pb-1">
            Favorites
          </div>
          <div className="overflow-y-auto sidebar-scroll-container max-h-[40vh]">
            <nav className="space-y-1 px-2 pb-2">
              {favItems.map((item) => (

                <Link
                  key={item.href}
                  href={item.href}
                  className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors ${pathname === item.href
                      ? "bg-sidebarItemSelected"
                      : "hover:bg-sidebarBackgroundHover"
                    }`}
                >
                  <img
                    src={"/assets/icons/fav_icon.svg"}
                    alt={item.label}
                    className="w-4 h-4"
                  />
                  {isOpen && (
                    <span className="flex-1 text-left text-sm text-sidebarFavText">
                      {item.label}
                    </span>
                  )}
                </Link>
              ))}
            </nav>
          </div>
        </div>
      </div>
    </aside>
  );
}

function trimPath(path: string) {
  try {
    const pathSegments = path.split("/").filter((segment) => segment);
    if (pathSegments[0] === "projects") return "/projects";
    if (pathSegments.length <= 2) return path;
    const trimmedPath = pathSegments.slice(0, 2).join("/");
    return `/${trimmedPath}`;
  } catch (error) {
    console.error("Invalid URL:", error);
    return null;
  }
}

function NavLink({
  item,
  pathname,
  isSidebarOpen,
  expandedItems,
  toggleItem,
  activekey,
  setActiveKey,
}: {
  item: NavItem;
  pathname: string;
  isSidebarOpen: boolean;
  expandedItems: { [key: string]: boolean };
  toggleItem: (title: string) => void;
  activekey: string | null;
  setActiveKey: (key: string) => void;
}) {
  const isAnyChildActive = (currentItem: NavItem): boolean => {
    if (!currentItem.children) return false;
    return currentItem.children.some((child) => trimPath(pathname) === child.href);
  };

  const isActive = trimPath(pathname) === item.href;

  return (
    <div>
      <Link
        href={item.href}
        onClick={() => {
          if (item.children) {
            toggleItem(item.label);
          }
          setActiveKey(item.key !== "operations" ? item.key : "deployed-projects");
        }}
        className={`w-full flex items-center gap-3 px-3 py-3 transition-colors ${isActive && item.key !== "operations"
            ? "bg-sidebarItemSelected"
            : "hover:bg-sidebarBackgroundHover"
          }`}
      >
        <img
          src={`/assets/icons/${isActive || isAnyChildActive(item) ? item.iconSelected : item.icon
            }.svg`}
          alt={item.label}
          className="w-5 h-5"
        />
        {isSidebarOpen && (
          <>
            <span className="flex-1 text-left">{item.label}</span>
            {item.children && (
              <img
                src="/assets/icons/chevron.svg"
                className={`w-4 h-4 transform transition-transform text-#888FAA ${expandedItems[item.label] ? "rotate-90" : "rotate-0"
                  }`}
                alt="chevron"
              />
            )}
          </>
        )}
      </Link>

      {item.children && isSidebarOpen && expandedItems[item.label] && (
        <div>
          {item.children.map((child) => (
            <Link
              key={child.href}
              href={child.href}
              onClick={() => {
                setActiveKey(child.key);
              }}
              className={`block px-3 py-3 transition-colors ${trimPath(pathname) === child.href
                  ? "bg-sidebarItemSelected"
                  : "hover:bg-sidebarBackgroundHover"
                }`}
            >
              <span className="ml-8 block">{child.label}</span>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
}
