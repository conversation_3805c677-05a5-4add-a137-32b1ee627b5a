"use client";
import React, { useState, useMemo, useRef, useEffect } from "react";
import SearchBar from "@/components/users/SearchBar";
import CustomAddDepartmentButton from "./CustomAddDepartmentButton";
import DepartmentCard from "./DepartmentCard";
import { useRouter } from "next/navigation";
import DepartmentDrawer from "./DepartmentDrawer";
import departmentService, {
  Department,
  DepartmentsResponse,
} from "@/service/departments";
import Loader from "../common/loader/loader";

export default function DepartmentsSection() {
  const [loading, setLoading] = useState<boolean>(false);
  const [search, setSearch] = useState("");
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [mode, setMode] = useState<"add" | "edit">("add");
  const [currentDept, setCurrentDept] = useState<Department | undefined>(
    undefined
  );
  const [allDeps, setAllDeps] = useState<DepartmentsResponse>();

  const router = useRouter();
  const scrollRef = useRef<HTMLDivElement>(null);

  const filtered = useMemo(
    () =>
      allDeps?.data.departments.departments.filter((d) =>
        d.name.toLowerCase().includes(search.toLowerCase())
      ),
    [search, allDeps]
  );

  const openAdd = () => {
    setMode("add");
    setCurrentDept(undefined);
    setDrawerOpen(true);
  };

  const openEdit = (id: string) => {
    const d = allDeps?.data.departments.departments.find(
      (d) => d._id === id
    )!;
    setMode("edit");
    setCurrentDept(d);
    setDrawerOpen(true);
  };

  const fetchAllDepartments = async () => {
    try {
      setLoading(true);
      const res = await departmentService.getAllDepartmentList();
      setAllDeps(res);
    } catch (error) {
      // console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = () => {
    console.log("i am here");
    fetchAllDepartments();
  };

  useEffect(() => {
    fetchAllDepartments();
  }, []);

  return (
    <div className="px-8 py-4">
      <div className="flex flex-col sm:flex-row w-full gap-4 mb-6">
        <SearchBar value={search} onChange={(e) => setSearch(e.target.value)} />
        <CustomAddDepartmentButton onClick={openAdd} />
      </div>

      {loading ? (
        <Loader />
      ) : filtered?.length === 0 ? (
        <p className="text-center text-gray-500 mt-4">
          No departments found for "{search}"
        </p>
      ) : (
        <div
          ref={scrollRef}
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
        >
          {filtered?.map((dept) => (
            <DepartmentCard
              key={dept._id}
              dept={dept}
              onClick={() => openEdit(dept._id)}
            />
          ))}
        </div>
      )}

      <DepartmentDrawer
        mode={mode}
        isOpen={drawerOpen}
        dept={currentDept}
        onClose={() => setDrawerOpen(false)}
        onSave={handleSave}
      />
    </div>
  );
}
