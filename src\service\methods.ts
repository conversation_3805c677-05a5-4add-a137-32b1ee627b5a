// token set & clear --> setToken, clearToken
// axios instance --> axiosInstance
// get, put, post, delete

import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";

// Create Axios instance with base configuration
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || "http://18.234.247.164:5004/api", // MAKE SURE TO PUT OUR API URL IN .env.local file

    headers: {
      "Content-Type": "application/json",
    },
  });

  // Request interceptor for adding auth token
  instance.interceptors.request.use(
    (config) => {
      const token = getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for handling errors
  instance.interceptors.response.use(
    (response) => response,
    (error: AxiosError) => {
      if (error.response?.status === 401) {
        clearToken();
        // window.location.href = "/login";
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

const axiosInstance = createAxiosInstance();

// Generic response type
export type ApiResponse<T = any> = {
  data: T;
  status: number;
  message?: string;
};

/**
 * HTTP methods for making API requests
 */
export const http = {
  get: async <T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response: AxiosResponse<T> = await axiosInstance.get(url, config);
      return { data: response.data, status: response.status };
    } catch (error) {
      const axiosError = error as AxiosError;
      throw {
        data: axiosError.response?.data,
        status: axiosError.response?.status || 500,
        message: axiosError.message,
      };
    }
  },

  post: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response: AxiosResponse<T> = await axiosInstance.post(url, data, config);
      return { data: response.data, status: response.status };
    } catch (error) {
      const axiosError = error as AxiosError;
      throw {
        data: axiosError.response?.data,
        status: axiosError.response?.status || 500,
        message: axiosError.message,
      };
    }
  },

  put: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response: AxiosResponse<T> = await axiosInstance.put(url, data, config);
      return { data: response.data, status: response.status };
    } catch (error) {
      const axiosError = error as AxiosError;
      throw {
        data: axiosError.response?.data,
        status: axiosError.response?.status || 500,
        message: axiosError.message,
      };
    }
  },

  delete: async <T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response: AxiosResponse<T> = await axiosInstance.delete(url, config);
      return { data: response.data, status: response.status };
    } catch (error) {
      const axiosError = error as AxiosError;
      throw {
        data: axiosError.response?.data,
        status: axiosError.response?.status || 500,
        message: axiosError.message,
      };
    }
  },
  upload: async <T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response: AxiosResponse<T> = await axiosInstance.post(url, formData, {
        ...config,
        headers: {
          ...config?.headers,
          "Content-Type": "multipart/form-data",
        },
      });
      return { data: response.data, status: response.status };
    } catch (error) {
      const axiosError = error as AxiosError;
      throw {
        data: axiosError.response?.data,
        status: axiosError.response?.status || 500,
        message: axiosError.message,
      };
    }
  },
};

// Token management
export const getToken = (): string | null => {
  return localStorage.getItem("authToken");
};

export const setToken = (token: string): void => {
  localStorage.setItem("authToken", token);
  axiosInstance.defaults.headers.common["Authorization"] = `Bearer ${token}`;
};

export const clearToken = (): void => {
  localStorage.removeItem("authToken");
  delete axiosInstance.defaults.headers.common["Authorization"];
};
