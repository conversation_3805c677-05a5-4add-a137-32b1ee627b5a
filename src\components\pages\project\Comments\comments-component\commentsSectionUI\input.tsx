"use client"

import { useState } from "react"

interface CommentInputProps {
  onSubmit: (comment: string) => Promise<void>
}

export function CommentInput({ onSubmit }: CommentInputProps) {
  const [comment, setComment] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async () => {
    if (!comment.trim()) return

    setIsSubmitting(true) // Set submitting state
    try {
      await onSubmit(comment) // Call API
      setComment("") // Clear input after success
    } finally {
      setIsSubmitting(false) // Reset submitting state
    }
  }

  return (
    <div className="space-y-4 p-4 border-b-2 border-[#CFD2DE]">
      <input
        placeholder="Make a comment"
        type="text"
        className="w-full px-4 py-2 border text-black rounded-md focus:outline-none focus:border-[#00B2A1] border-gray-300 placeholder-[#3b4154]"
        value={comment}
        onChange={(e) => setComment(e.target.value)}
      />

      <div className="flex justify-end space-x-2 mt-2">
        {/* Cancel Button */}
        <button
          className={`px-4 py-2 rounded-md border-none cursor-pointer text-sm bg-transparent ${comment.trim() ? "text-[#00B2A1]" : "text-[#888faa] cursor-not-allowed"
            }`}
          disabled={!comment.trim() || isSubmitting}
          onClick={() => setComment("")}
        >
          Cancel
        </button>

        {/* Submit Button */}
        <button
          className={`px-8 py-2 rounded-[4px] border-none cursor-pointer text-sm ${comment.trim() ? "bg-[#00B2A1] text-white" : "bg-[#CED2DE] text-[#888faa] cursor-not-allowed"
            }`}
          disabled={!comment.trim() || isSubmitting}
          onClick={handleSubmit}
        >
          {isSubmitting ? "Submitting..." : "Submit"}
        </button>
      </div>
    </div>
  )
}
