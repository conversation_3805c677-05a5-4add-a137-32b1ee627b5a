"use client"

import { useEffect, useState, useRef } from "react"
import { useDispatch, useSelector } from "react-redux"
import { motion } from "framer-motion"
import { fetchAllValueAnalytics, fetchFilteredValueAnalytics } from "@/lib/store/features/va/valueAnalyticsSlice"
import type { RootState, AppDispatch } from "@/lib/store"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ProjectSelector } from "@/components/pages/ValueAnalytics/project-selector"
import { DashboardOverview } from "@/components/pages/ValueAnalytics/dashboard-overview"
import { DashboardBreakdown } from "@/components/pages/ValueAnalytics/dashboard-breakdown"
import Loader from "@/components/common/loader/loader"
import { BlurOverlay } from "@/components/common/blur-overlay/blur-overlay"
import { valueAnalyticsService } from "@/service/api"
import { selectValuePillarIdByName } from '@/lib/store/features/va/valuePillarsSlice'
import { fetchValuePillars } from "@/lib/store/features/va/valuePillarsSlice"

// Flag to toggle blur overlay with design elements
const SHOW_DESIGN_OVERLAY = false;

export default function RevenueGrowthPage() {
  const dispatch = useDispatch<AppDispatch>()
  const valuePillarsStatus = useSelector((state: RootState) => state.valuePillars.status);
  const valuePillars = useSelector((state: RootState) => state.valuePillars.pillars);
  const [timeRange, setTimeRange] = useState("ALL")
  const [selectedProjectId, setSelectedProjectId] = useState<string>("all")
  const [isLoading, setIsLoading] = useState(true)
  const [pillarData, setPillarData] = useState<any>(null)
  const revenueGrowthPillarId = useSelector((state: any) => selectValuePillarIdByName(state, 'Revenue Growth'));
  const hasFetchedRef = useRef(false);

  useEffect(() => {
    if (!hasFetchedRef.current && (valuePillarsStatus === 'idle' || valuePillars.length === 0)) {
      dispatch(fetchValuePillars());
      hasFetchedRef.current = true;
    }
  }, [dispatch, valuePillarsStatus, valuePillars.length]);

  useEffect(() => {
    const fetchPillarData = async () => {
      if (!revenueGrowthPillarId) return;
      try {
        const pillarResponse = await valueAnalyticsService.getValuePillarById(revenueGrowthPillarId);
        setPillarData(pillarResponse.data.data);
      } catch (error) {
        console.error('Error fetching pillar data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchPillarData();
  }, [revenueGrowthPillarId]);

  const getDateRange = (range: string): { startDate: string | undefined; endDate: string | undefined } => {
    const today = new Date()
    let startDate: Date | undefined = undefined

    switch (range) {
      case 'LAST_MONTH': {
        startDate = new Date(today)
        startDate.setMonth(today.getMonth() - 1)
        break
      }
      case 'LAST_QUARTER': {
        startDate = new Date(today)
        startDate.setMonth(today.getMonth() - 3)
        break
      }
      case 'YEAR_TO_DATE': {
        startDate = new Date(today)
        startDate.setMonth(today.getMonth() - 12)
        break
      }
      default: {
        return { startDate: undefined, endDate: undefined }
      }
    }

    return {
      startDate: startDate.getTime().toString(),
      endDate: ""
    }
  }

  const handleProjectSelect = (projectId: string) => {
    setSelectedProjectId(projectId)
  }

  // Design elements for overlays
  const overviewDesignElement = (
    <div className="bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg p-8 text-white text-center">
      <div className="text-4xl font-bold mb-4 underline">COMING SOON</div>
      <div className="text-xl">We're building more Value Analytics dashboards.<br />Providing you even more insight in to your realized value.<br />They'll be ready very soon!</div>
    </div>
  );

  if (isLoading) {
    return <Loader />
  }


  const filteredProjects = selectedProjectId === "all"
    ? (pillarData?.projects || [])
    : (pillarData?.projects || []).filter((project: any) => project.id === selectedProjectId);

  // Time range filtering for monthlyAggregation
  const getFilteredMonthlyAggregation = () => {
    if (!pillarData?.monthlyAggregation) return [];
    if (timeRange === "ALL") return pillarData.monthlyAggregation;
    const today = new Date();
    let startDate: Date | undefined = undefined;
    switch (timeRange) {
      case 'LAST_MONTH': {
        startDate = new Date(today);
        startDate.setMonth(today.getMonth() - 1);
        break;
      }
      case 'LAST_QUARTER': {
        startDate = new Date(today);
        startDate.setMonth(today.getMonth() - 3);
        break;
      }
      case 'YEAR_TO_DATE': {
        startDate = new Date(today);
        startDate.setMonth(today.getMonth() - 12);
        break;
      }
      default: return pillarData.monthlyAggregation;
    }
    return pillarData.monthlyAggregation.filter((item: any) => {
      const itemDate = new Date(item.year, item.month - 1);
      return startDate ? itemDate >= startDate : true;
    });
  };

  // Calculate hardValue, softValue, totalValue from filtered projects
  let hardValue = 0;
  let softValue = 0;
  filteredProjects.forEach((project: any) => {
    project.valuePillars.forEach((pillar: any) => {
      if (pillar.name === "Revenue Growth") {
        pillar.benefits.forEach((benefit: any) => {
          benefit.metrics.forEach((metric: any) => {
            if (metric.dollarType === "Hard") {
              hardValue += metric.dollarValue || 0;
            } else if (metric.dollarType === "Soft") {
              softValue += metric.dollarValue || 0;
            }
          });
        });
      }
    });
  });
  const totalValue = hardValue + softValue;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="p-6 space-y-6 min-h-screen"
    >
      
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Revenue Growth</h1>
          <p>{pillarData?.about || "Strategies to increase overall revenue streams."}</p>
        </div>
        <div className="flex space-x-4">
          <ProjectSelector
            projects={pillarData?.projects || []}
            selectedProjectId={selectedProjectId}
            onSelectProject={handleProjectSelect}
            disabled={false}
          />
          <Select value={timeRange} onValueChange={setTimeRange} disabled={false}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Time</SelectItem>
              <SelectItem value="LAST_MONTH">Last Month</SelectItem>
              <SelectItem value="LAST_QUARTER">Last Quarter</SelectItem>
              <SelectItem value="YEAR_TO_DATE">Year To Date</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <BlurOverlay
        showBlur={SHOW_DESIGN_OVERLAY}
        designElement={overviewDesignElement}
      >
        <DashboardOverview 
          totalValue={totalValue}
          hardValue={hardValue}
          softValue={softValue}
          monthlyAggregation={getFilteredMonthlyAggregation().map((item: any) => ({
            year: item.year,
            month: item.month,
            totalDollarValue: item.totalDollarValue,
            hardDollarValue: item.hardDollarValue,
            softDollarValue: item.softDollarValue
          })) || []}
        />
        <br />
        <DashboardBreakdown projects={filteredProjects} />
      </BlurOverlay>
    </motion.div>
  )
}
