import React, { useState, useEffect } from "react";
import { X, Info, ChevronDown, Loader2 } from "lucide-react";
import { datasetService } from "@/service/datasetService";
import { toast, ToastContainer } from "react-toastify";
import Image from "next/image";

interface EditDatasetProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  datasetId: string;
  initialData: any;
  onUpdate: () => void;
}

const EditDataset: React.FC<EditDatasetProps> = ({ isOpen, setIsOpen, datasetId, initialData, onUpdate }) => {
  const [formData, setFormData] = useState({
    visibility: initialData.visibility || "private",
    title: initialData.title || "",
    description: initialData.description || "",
    tags: initialData.tags || [],
    source: initialData.source || "",
    license: initialData.license || "",
    department: initialData.department || [],
    isWorldDataset: initialData.isWorldDataset,
  });

  const [isPrivate, setIsPrivate] = useState(formData.visibility === "private");
  const [isTagDropdownOpen, setIsTagDropdownOpen] = useState(false);
  const [searchTag, setSearchTag] = useState("");
  const [tagList, setTagList] = useState<any>(null);
  const [isLicenseDropdownOpen, setIsLicenseDropdownOpen] = useState(false);
  const [licenseList, setLicenseList] = useState<any>(null);
  const [isDepartmentDropdownOpen, setIsDepartmentDropdownOpen] = useState(false);
  const [departmentList, setDepartmentList] = useState<any>(null);
  const [checkedDepartments, setCheckedDepartments] = useState<string[]>([]);
  const [exploreUrlQuery, setExploreUrlQuery] = useState({
    page: 1,
    limit: 10,
    search: "",
  });
  const [isUpdating, setIsUpdating] = useState(false);

  const toggleTag = (tagName: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.includes(tagName) ? prev.tags.filter((tag: string) => tag !== tagName) : [...prev.tags, tagName],
    }));
    setIsTagDropdownOpen(false);
  };

  const fetchTagsData = async (search: string) => {
    if (!search) {
      setTagList(null);
      return;
    }
    try {
      const result = await datasetService.getTagList({
        search,
        type: "tag",
        page: 1,
        limit: 10,
      });
      setTagList(result.data);
      setIsTagDropdownOpen(true);
    } catch (err) {
      console.error("Failed to fetch tag data.", err);
    }
  };

  const fetchLicenseData = async () => {
    try {
      const result = await datasetService.getTagList({
        type: "license",
        page: 1,
        limit: 10,
      });
      setLicenseList(result.data);
    } catch (err) {
      console.error("Failed to fetch license data.", err);
    }
  };

  const fetchExploreData = async () => {
    try {
      const result = await datasetService.getDepartmentList({
        page: exploreUrlQuery.page,
        limit: exploreUrlQuery.limit,
      });
      setDepartmentList(result.data);

      if (initialData.department && initialData.department.length > 0) {
        const deptNames = result.data.departments
          .filter((dept: any) => initialData.department.includes(dept._id))
          .map((dept: any) => dept.name);
        setCheckedDepartments(deptNames);
      }
    } catch (err) {
      console.error("Failed to fetch department data.", err);
    }
  };

  const toggleDepartment = (deptName: string, deptId: string) => {
    setFormData((prevData) => ({
      ...prevData,
      department: prevData.department.includes(deptId)
        ? prevData.department.filter((d: string) => d !== deptId)
        : [...prevData.department, deptId],
    }));

    setCheckedDepartments((prev) =>
      prev.includes(deptName) ? prev.filter((d) => d !== deptName) : [...prev, deptName]
    );

    setIsDepartmentDropdownOpen(false);
  };

  const handleTagSubmit = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      const value = searchTag.trim();
      if (!value) return;

      if (!tagList?.tags?.some((tag: any) => tag.name.toLowerCase() === value.toLowerCase())) {
        toggleTag(value);
      }
      setSearchTag("");
    }
  };

  const checkDatasetTitle = async (title: string) => {
    const result: any = await datasetService.checkDatasetTitle(title);
    // console.log("CHECK DATASET TITLE", result);
    return result;
  };

  //   const handleUpdateDataset = async () => {
  //     setIsUpdating(true);
  //     try {
  //       const titleExists = await checkDatasetTitle(formData.title);
  //       if (titleExists.data.exists) {
  //         toast.error("Dataset title already exists");
  //       } else {
  //         const response = await datasetService.updateDataset(datasetId, {
  //           ...formData,
  //           visibility: isPrivate ? "private" : "public",
  //           department: formData.department,
  //         });

  //         if (response.status === 200) {
  //           onUpdate();
  //           setIsOpen(false);
  //           toast.success("Dataset updated successfully!");
  //         } else {
  //           toast.error("Failed to update dataset details");
  //         }
  //       }
  //     } catch (error) {
  //       console.error("Error updating dataset:", error);
  //       toast.error("Failed to update dataset details");
  //     } finally {
  //       setIsUpdating(false);
  //     }
  //   };

  const handleUpdateDataset = async () => {
    setIsUpdating(true);
    if (formData.department.length === 0) {
      toast.error("Please select at least one department");
      setIsUpdating(false);
      return;
    }
    try {
      let updatedData = { ...formData, visibility: isPrivate ? "private" : "public" };

      // Only check and send the title if it has changed
      if (formData.title !== initialData.title) {
        const titleExists = await checkDatasetTitle(formData.title);
        if (titleExists.data.exists) {
          toast.error("Dataset title already exists");
          setIsUpdating(false);
          return;
        }
      } else {
        delete updatedData.title; // Remove title from the update payload if unchanged
      }

      const response = await datasetService.updateDataset(datasetId, updatedData);

      if (response.status === 200) {
        // toast.success("Dataset updated successfully!");
        onUpdate();
        setIsOpen(false);
      } else {
        toast.error("Failed to update dataset details");
      }
    } catch (error) {
      console.error("Error updating dataset:", error);
      toast.error("Failed to update dataset details");
    } finally {
      setIsUpdating(false);
    }
  };

  useEffect(() => {
    fetchLicenseData();
    fetchExploreData();
  }, [exploreUrlQuery]);

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (!event.target.closest(".department-dropdown")) {
        setIsDepartmentDropdownOpen(false);
      }
      if (!event.target.closest(".tags-dropdown")) {
        setIsTagDropdownOpen(false);
      }
      if (!event.target.closest(".license-dropdown")) {
        setIsLicenseDropdownOpen(false);
      }
    };
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  return (
    <div
      className={`z-50 fixed top-0 right-0 h-screen w-[calc(100%-210px)] bg-white shadow-lg transform ${isOpen ? "translate-x-0" : "translate-x-full"
        } transition-transform duration-300 ease-in-out flex flex-col`}
    >
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss={false}
        draggable
        pauseOnHover={false}
      />
      <div className="flex sticky h-12 justify-between bg-[#3B4154] text-white p-5 items-center">
        <h2 className="text-lg">Edit Dataset</h2>
        <button onClick={() => setIsOpen(false)} className="text-xl">
          <X />
        </button>
      </div>

      <div className="flex-1 overflow-y-auto p-6">
        <div className="mb-6 flex gap-2 items-center">
          <div className="flex items-center gap-2">
            <label className="block">
              Visibility <span className="text-red-500">*</span>
            </label>
            <div className="tooltip">
              <Info className="w-4 h-4 text-gray-400" />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div
              className={`flex items-center border rounded-full p-0.5 w-[48px] cursor-pointer transition-all duration-300 ${isPrivate ? "bg-teal-500" : "bg-gray-300"
                }`}
              onClick={() => setIsPrivate(!isPrivate)}
            >
              <div
                className={`w-5 h-5 rounded-full bg-white shadow-md transform transition-transform duration-300 ${isPrivate ? "translate-x-[22px]" : "translate-x-0"
                  }`}
              ></div>
            </div>
            <span className="text-m text-gray-800">{false ? "Private" : "Public"}</span>
          </div>
        </div>

        <div className="mb-6">
          <label className="block mb-2">
            Title <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            className="w-full p-2 border rounded focus:outline-none focus:border-teal-500"
            placeholder="Enter dataset title"
          />
        </div>

        <div className="mb-6">
          <label className="block mb-2">Description</label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            className="w-full p-2 border rounded focus:outline-none focus:border-teal-500"
            rows={4}
            placeholder="Enter dataset description"
          />
        </div>

        <div className="mb-6 relative">
          <label className="block mb-2">
            Select Department <span className="text-red-500">*</span>
          </label>
          <div
            className="w-full p-2 border rounded focus:outline-none focus:border-teal-500 cursor-pointer flex justify-between items-center department-dropdown"
            onClick={() => setIsDepartmentDropdownOpen(!isDepartmentDropdownOpen)}
          >
            <span>{checkedDepartments.length ? checkedDepartments.join(", ") : "Select Departments"}</span>
            <ChevronDown className="w-4 h-4" />
          </div>
          {isDepartmentDropdownOpen && (
            <div className="absolute left-0 right-0 mt-1 bg-white border rounded shadow-lg max-h-48 overflow-y-auto z-50">
              {departmentList?.departments?.map((department: any) => (
                <div
                  key={department._id}
                  className="p-2 hover:bg-gray-100 cursor-pointer flex items-center"
                  onClick={() => toggleDepartment(department.name, department._id)}
                >
                  <input
                    type="checkbox"
                    checked={checkedDepartments.includes(department.name)}
                    onChange={() => { }}
                    className="mr-2"
                  />
                  {department.name}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Tags Section */}
        <div className="mb-6 relative tags-dropdown">
          <label className="block mb-2">Tags</label>
          <div className="relative">
            <input
              type="text"
              value={searchTag}
              onChange={(e) => {
                setSearchTag(e.target.value);
                fetchTagsData(e.target.value);
              }}
              onKeyDown={handleTagSubmit}
              className="w-full p-2 border rounded focus:outline-none focus:border-teal-500"
              placeholder="Search tags"
            />
            {isTagDropdownOpen && (
              <div className="absolute z-10 w-full mt-1 bg-white border rounded shadow-lg">
                {searchTag.trim().length > 3 && (
                  <div
                    className="p-2 hover:bg-gray-100 cursor-pointer flex items-center "
                    onClick={() => {
                      toggleTag(searchTag.trim());
                      setSearchTag("");
                    }}
                  >
                    {searchTag.trim()}
                  </div>
                )}
                {tagList?.tags?.length > 0 ? (
                  tagList.tags.map((tag: any) => (
                    <div
                      key={tag.name}
                      className="p-2 hover:bg-gray-100 cursor-pointer"
                      onClick={() => {
                        toggleTag(tag.name);
                        setSearchTag("");
                      }}
                    >
                      {tag.name}
                    </div>
                  ))
                ) : (
                  <></>
                )}
              </div>
            )}
          </div>
          {formData.tags.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-2">
              {formData.tags.map((tag: string) => (
                <span key={tag} className="bg-gray-200 px-2 py-1 rounded flex items-center gap-1">
                  {tag}
                  <button onClick={() => toggleTag(tag)} className="ml-1 text-gray-500 hover:text-gray-700">
                    ×
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Source and License */}
        <div className="mb-6 flex gap-4">
          <div className="w-1/2">
            <label className="block mb-2">Source</label>
            <input
              type="text"
              value={formData.source}
              onChange={(e) => setFormData({ ...formData, source: e.target.value })}
              className="w-full p-2 border rounded focus:outline-none focus:border-teal-500"
              placeholder="Enter source"
            />
          </div>
          <div className="w-1/2 relative license-dropdown">
            <label className="block mb-2">License</label>
            <div
              className="w-full p-2 border rounded focus:outline-none focus:border-teal-500 cursor-pointer flex justify-between items-center"
              onClick={() => setIsLicenseDropdownOpen(!isLicenseDropdownOpen)}
            >
              <span>{formData.license || "Select License"}</span>
              <ChevronDown className="w-4 h-4" />
            </div>
            {isLicenseDropdownOpen && (
              <div className="absolute z-10 w-full mt-1 bg-white border rounded shadow-lg">
                {licenseList?.tags?.map((license: any) => (
                  <div
                    key={license._id}
                    className="p-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => {
                      setFormData({ ...formData, license: license.name });
                      setIsLicenseDropdownOpen(false);
                    }}
                  >
                    {license.name}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="bg-white border-t p-4 flex justify-end gap-4">
        <button
          className="px-4 py-2 border rounded hover:bg-gray-50"
          onClick={() => setIsOpen(false)}
          disabled={isUpdating}
        >
          Cancel
        </button>
        <button
          className="px-4 py-2 bg-teal-500 text-white rounded hover:bg-teal-600 flex items-center gap-2"
          onClick={handleUpdateDataset}
          disabled={isUpdating}
        >
          {isUpdating ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              Updating...
            </>
          ) : (
            "Update Details"
          )}
        </button>
      </div>
    </div>
  );
};

export default EditDataset;
