import React, { useState, useEffect } from "react";
import OverviewTab from "@/components/pages/project/tabs/overview";
import AssetsTab from "@/components/pages/project/tabs/assets";
import DeploymentTab from "@/components/pages/project/tabs/deployment";
import ManageTab, { ToastState } from "@/components/pages/project/tabs/manage";
import Image from "next/image";
import { useParams } from "next/navigation";
import {
  getProjectComment,
  getProjectDetails,
  getProjectIdeUrl,
} from "@/service/projects";
import { ProjectDetails } from "@/service/types/types";
import { useDispatch, useSelector } from "react-redux";
import { setAiAvatarStatus } from "@/lib/store/features/project/projectSlice";
import { RootState } from "@/lib/store";
import { setProjectActiveTab } from "@/lib/store/features/project/projectTabSlice";
import Comment from "@/components/pages/project/tabs/comments";
import { useRouter } from "next/navigation";
import { Toast } from "@/components/ui/toast/toast";
import { setCommentCount } from "@/lib/store/features/project/projectCommentSlice";
import AiOverview from "./tabs/ai_overview";
import AgentX from "../AgentX/AgentX";
import Loader from "@/components/common/loader/loader";
import { useSidebar } from "@/context/sidebar-context";

const ProjectDashboard = () => {
  const { id } = useParams() as { id: string };
  // console.log(id);
  const dispatch = useDispatch();
  const userInfo = useSelector((state: any) => state.user.userInfo);
  const activeTab = useSelector((state: RootState) => state.tab.activeTab);
  const tabs = ["Overview", "Assets", "Manage", "Deployment"];
  const Aitabs = ["Overview", "Manage"];
  const [projectDetails, setProjectDetails] = useState<ProjectDetails | null>(
    null
  );
  const [ideLoading, setIdeLoading] = useState<boolean>(false);
  const [toast, setToast] = useState<ToastState>(null);
  const [isAiProject, setIsAiProject] = useState(false);
  const router = useRouter();
  const [getProjectDetailsLoader, setGetProjectDetailsLoader] =
    useState<Boolean>(true);
  const { toggleSidebar, isOpen } = useSidebar();

  const fetchProjectDetails = async (
    calledWithinComponent: boolean = false
  ) => {
    if (calledWithinComponent) {
      setGetProjectDetailsLoader(true);
    }
    if (!id) return;
    try {
      const response = await getProjectDetails(id);
      const data = response.data;
      setProjectDetails(data);
      setIsAiProject(data?.isAiBox || false);
      dispatch(setAiAvatarStatus({ status: data?.aiAvatar || false }));
    } catch (error) {
      console.error("Error fetching project details:", error);
    } finally {
      if (calledWithinComponent) {
        setGetProjectDetailsLoader(false);
      }
    }
  };

  useEffect(() => {
    fetchProjectDetails(true);
  }, [id]);

  useEffect(() => {
    const fetchComments = async () => {
      try {
        const response = await getProjectComment(id || "");
        dispatch(setCommentCount({ commentCount: response.data.comments.length || 0 }));
      } catch (error) {
        console.error("Failed to fetch comments:", error);
      }
    };
    fetchComments();
  }, []);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  function fixReturnTo(url: string) {
    const urlObj = new URL(url);
    const returnTo = urlObj.searchParams.get("return_to");

    if (returnTo) {
      const fixedReturnTo = encodeURIComponent(returnTo);
      urlObj.searchParams.set("return_to", fixedReturnTo);
    }

    return urlObj.toString();
  }

  const getIdeUrl = async () => {
    setIdeLoading(true);
    try {
      const response = await getProjectIdeUrl(projectDetails?._id || "");
      const fixedUrl = encodeURIComponent(fixReturnTo(response.data.ide_url));
      router.push(
        `/projects/console/${projectDetails?._id}?name=${projectDetails?.name}&consoleUrl=${fixedUrl}&appType=${projectDetails?.appType}`
      );
      if (isOpen) {
        toggleSidebar();
      }
    } catch (error) {
      setToast({
        message: "Not able to get Console URL",
        type: "error",
      });
      console.error("API Error", error);
    } finally {
      setIdeLoading(false);
    }
  };

  const isAvatarEnable = useSelector(
    (state: RootState) => state.projectAi.isAvatarEnable
  );

  const hasAccess = (user: any): boolean => {
    if (user.user.isSysAdmin) {
      return isAvatarEnable;
    }

    const userRole = projectDetails?.currentUserRole?.userRole;
    const isDashboard = projectDetails?.appType === "Dashboard";

    return (userRole === "Executive" || userRole === "Creator") && (isDashboard || !isAvatarEnable);
  };


  const isLaunchEnable = hasAccess(userInfo)

  if (getProjectDetailsLoader || ideLoading) {
    return <Loader />;
  }

  return (
    <div className="p-8">
      <div className="flex items-center justify-between gap-3">
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
            dismissTime={5000}
          />
        )}
        <h1 className="text-xl font-medium flex-1">{projectDetails?.name}</h1>
        {!isAiProject && (
          <>
            {isAvatarEnable ? (
              <div className="flex gap-1">
                <Image
                  src="/assets/icons/ai-avatar-icon.svg"
                  alt="Logo"
                  width={18}
                  height={18}
                  objectFit="contain"
                />
                <p className="text-sm">Supported by AI Avatar</p>
              </div>
            ) : (
              <div></div>
            )}
            <div className="relative group">
              <button
                className={`w-40 h-8 ${!isLaunchEnable
                  ? "bg-gray-300 cursor-not-allowed"
                  : "bg-[#00B2A1]"
                  } bg-[#00B2A1] text-white text-sm py-[6px] px-4 rounded-[4px] flex items-center justify-center`}
                onClick={getIdeUrl}
                disabled={ideLoading || !isLaunchEnable}
              >
                {ideLoading ? (
                  <div className="animate-spin border-2 border-white border-t-transparent rounded-full h-5 w-5"></div>
                ) : (
                  "Launch AI Builder"
                )}
              </button>
              {/* Tooltip appears when button is disabled and hovered */}
              {!isLaunchEnable && (
                <span className="absolute z-[999] top-[-40px] left-1/2 -translate-x-1/2 mt-2 max-w-[220px] px-2 py-1 bg-white text-[#555] text-[12.5px] text-center rounded-md shadow-[0px_0px_12px_#00000029] opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                  You don't have access to AI Builder
                </span>
              )}
            </div>
          </>
        )}
      </div>
      {projectDetails?.appType !== "Dashboard" && !projectDetails?.isAiBox && isAvatarEnable && <AgentX projectId={id} />}
      <div className="flex justify-between border-y mt-4 items-center">
        <div className="flex space-x-4">
          {isAiProject ? (
            <>
              {Aitabs.map((tab) => (
                <button
                  key={tab}
                  className={`px-8 py-2 text-sm ${activeTab === tab
                    ? "border-b-2 border-[#00B2A1] text-[#3B4154] font-semibold"
                    : "text-[#3B4154]"
                    }`}
                  onClick={() => dispatch(setProjectActiveTab(tab))}
                >
                  {tab}
                </button>
              ))}
            </>
          ) : (
            <>
              {tabs.map((tab) => (
                <div key={tab}>
                  {projectDetails?.appType == "Dashboard" && tab == "Assets" ? (
                    <></>
                  ) : (
                    <>
                      {tab === "Deployment" && isAvatarEnable && !isLaunchEnable ? (
                        <></>
                      ) : (
                        <button
                          key={tab}
                          className={`px-8 py-2 text-sm ${activeTab === tab
                            ? "border-b-2 border-[#00B2A1] text-[#3B4154] font-semibold"
                            : "text-[#3B4154]"
                            }`}
                          onClick={() => dispatch(setProjectActiveTab(tab))}
                        >
                          {tab}
                        </button>
                      )}
                    </>
                  )}
                </div>
              ))}
            </>
          )
          }
        </div>
        {
          !isAiProject && (
            <div className="ml-auto">
              <Comment projectDetails={projectDetails} />
            </div>
          )
        }
      </div >

      <div className="h-full">
        {activeTab === "Overview" ? (
          isAiProject ? (
            <AiOverview aiProjectDetails={projectDetails} />
          ) : (
            <OverviewTab projectDetails={projectDetails} />
          )
        ) : (
          <></>
        )}
        {activeTab === "Assets" && projectDetails?.appType !== "Dashboard" && (
          <AssetsTab projectDetails={projectDetails} />
        )}
        {activeTab === "Manage" && (
          <ManageTab
            projectDetails={projectDetails}
            fetchProjectDetails={fetchProjectDetails}
            isAiProject={isAiProject}
          />
        )}
        {activeTab === "Deployment" && (
          <DeploymentTab projectDetails={projectDetails} />
        )}
      </div>
    </div >
  );
};

export default ProjectDashboard;
