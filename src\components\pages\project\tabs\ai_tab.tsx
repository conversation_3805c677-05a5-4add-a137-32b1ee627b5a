import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Divider from '@/components/common/divider/divider';
import AIAvatarDisableModal from '@/components/common/avatarDialog/avatar_dialog'; // Import the modal
import { ProjectDetails } from '@/service/types/types';
import { updateProject } from '@/service/projects';
import { useDispatch } from "react-redux";
import { setAiAvatarStatus } from "@/lib/store/features/project/projectSlice";

const AIAvatarToggle = ({
    projectDetails,
}: {
    projectDetails: ProjectDetails | null;
}) => {
    const [isEnabled, setIsEnabled] = useState(projectDetails?.aiAvatar || false);
    const [showModal, setShowModal] = useState(false);
    const [dontShowAgain, setDontShowAgain] = useState(false);
    const dispatch = useDispatch();
    const userRole = projectDetails?.currentUserRole?.userRole;

    useEffect(() => {
        const dontShowAgainSetting = localStorage.getItem('aiAvatarDontShowAgain');
        if (dontShowAgainSetting === 'true') {
            setDontShowAgain(true);
        }
    }, []);

    useEffect(() => {
        localStorage.removeItem('aiAvatarDontShowAgain'); // Clear it on page reload (for development only)
    }, []);

    const handleToggle = async () => {
        if (userRole != "Executive" && userRole != "Creator") return;
        if (isEnabled && !dontShowAgain) {
            setShowModal(true);
        } else {
            try {
                const res = await updateProject({ aiAvatar: !isEnabled }, projectDetails?._id || "");
                // console.log(res);
                dispatch(setAiAvatarStatus({ status: !isEnabled })); // Toggle to true
                if (res.status == 200 || res.status == 201) {
                    setIsEnabled(!isEnabled);
                }
            } catch (error) {
                console.error("error", error);
            }
        }
    };

    const handleConfirmDisable = async () => {
        setShowModal(false);
        if (dontShowAgain) {
            localStorage.setItem('aiAvatarDontShowAgain', 'true');
        }
        try {
            const res = await updateProject({ aiAvatar: false }, projectDetails?._id || "");
            dispatch(setAiAvatarStatus({ status: false }));
            if (res.status == 200 || res.status == 201) {
                // console.log(res);
                setIsEnabled(false);
            }
        } catch (error) {
            console.error("error", error);
        }
    };
    return (
        <div>
            <h2 className="p-4 text-xl text-[#3B4154]">AI Avatar</h2>
            <Divider color="#CCC" thickness="0.6px" />
            <div className="flex items-center gap-2 px-4 py-3">
                <Image
                    src="/assets/icons/ai-avatar-icon.svg"
                    alt="AI Avatar Icon"
                    width={20}
                    height={20}
                />
                <span className="text-lg font-semibold text-[#3B4154]">AI Avatar</span>
                <button
                    onClick={handleToggle}
                    disabled={userRole != "Executive" && userRole != "Creator"}
                    className={`w-10 h-5 flex items-center ${isEnabled ? 'bg-[#00B2A1]' : 'bg-[#d1d5db]'}
                    ${userRole != "Executive" && userRole != "Creator" ? "cursor-not-allowed" : ""} rounded-full px-1 py-2 transition-colors duration-300`}
                >
                    <div
                        className={`w-4 h-4 bg-white rounded-full shadow-md transform transition-transform duration-300 ${isEnabled ? 'translate-x-4' : 'translate-x-0'}`}
                    ></div>
                </button>
            </div>
            <p className="text-sm text-gray-500 px-4">
                AI Avatar is currently {isEnabled ? 'enabled' : 'disabled'}, click to {isEnabled ? 'disable' : 'enable'}
            </p>

            {/* Modal component */}
            <AIAvatarDisableModal
                showModal={showModal}
                setShowModal={setShowModal}
                dontShowAgain={dontShowAgain}
                setDontShowAgain={setDontShowAgain}
                handleConfirmDisable={handleConfirmDisable}
            />
        </div>
    );
};

export default AIAvatarToggle;
