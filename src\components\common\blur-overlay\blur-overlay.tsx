import React from 'react';

interface BlurOverlayProps {
  children: React.ReactNode;
  designElement?: React.ReactNode;
  showBlur?: boolean;
}

export const BlurOverlay: React.FC<BlurOverlayProps> = ({ 
  children, 
  designElement,
  showBlur = false
}) => {
  return (
    <div className="relative">
      <div className={`transition-all duration-300 ${showBlur ? 'blur-md' : ''}`}>
        {children}
      </div>
      
      {showBlur && designElement && (
        <div className="absolute inset-0 top-16 flex items-start justify-center pointer-events-none">
          {designElement}
        </div>
      )}
    </div>
  );
};
