"use client";

import { motion } from "framer-motion";
import {
  TbDatabase,
  TbChartArrowsVertical,
  TbBulb,
  TbTriangleSquareCircle,
  TbSettings,
  TbLogout,
  TbUserCircle,
  TbRocket,
  TbHome,
} from "react-icons/tb";
import { HiOutlineSupport } from "react-icons/hi";
import { cn } from "@/lib/utils";
import { NavSection } from "@/types/sidebar";
import Notification from "@/components/pages/Notification/Notification";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover/popover";
import { useState } from "react";
import Link from "next/link";
import { MdOutlineManageAccounts } from "react-icons/md";
import { useSelector } from "react-redux";

interface SlimSidebarProps {
  activeSection: string;
  onSectionClick: (section: NavSection) => void;
  [x: string]: any;
}

export function SlimSidebar(props: SlimSidebarProps) {
  const { activeSection, onSectionClick, handleLogout } = props;
  const [openPopover, setOpenPopover] = useState<string | null>(null);
  const userInfo = useSelector((state: any) => state.user.userInfo);
  const {
    user: { isSysAdmin },
  } = userInfo;
  const valuePillars = useSelector((state: any) => state.valuePillars.pillars);

  const navItems = [
    { id: "home", icon: TbHome, label: "Home" },
    { id: "value", icon: TbChartArrowsVertical, label: "Value Analytics" },
    { id: "opportunities", icon: TbBulb, label: "Opportunities" },
    { id: "apps", icon: TbRocket, label: "Apps" },
    { id: "projects", icon: TbTriangleSquareCircle, label: "Projects" },
    { id: "data", icon: TbDatabase, label: "Data" },
    { id: "support", icon: HiOutlineSupport, label: "Support" },
    // { id: "settings", icon: TbSettings, label: "Settings" },
    { id: "notifications", icon: Notification, label: "Notifications" },
    { id: "profile", icon: TbUserCircle, label: "Profile" },
  ];

  return (
    <div className="flex flex-col fixed inset-y-0 left-0 w-[80px] bg-[#3B4154] rounded-r-xl z-50">
      <Link href="/" className="px-3 py-2">
        <img src="/sutra-icon-white.svg" alt="SUTRA.ai Logo" className="h-[35px] w-[35px] mx-auto opacity-100" style={{ filter: 'none' }} />
      </Link>
      <nav className="flex-1 flex flex-col">
        <div className="flex-1 space-y-1">
          {navItems.slice(0, 6).map((item) => (
            <Popover
              key={item.id}
              open={openPopover === item.id}
              onOpenChange={(open) => setOpenPopover(open ? item.id : null)}
            >
              <PopoverTrigger asChild>
                <button
                  onMouseEnter={() => setOpenPopover(item.id)}
                  onMouseLeave={() => setOpenPopover(null)}
                  onClick={() => onSectionClick(item.id as NavSection)}
                  className={cn(
                    "relative w-full h-[60px] flex items-center justify-center text-slate-300 hover:text-white transition-colors",
                    activeSection === item.id && "text-white"
                  )}
                >
                  {activeSection === item.id && (
                    <motion.div
                      layoutId="activeSection"
                      className="absolute rounded-r-[4px] left-0 w-[6px] h-full bg-[#00B2A1]"
                    />
                  )}
                  <item.icon className="w-8 h-8" />
                </button>
              </PopoverTrigger>
              <PopoverContent
                side="right"
                className="w-auto py-2 px-4 text-lg font-medium"
                sideOffset={-12}
                onMouseEnter={() => setOpenPopover(item.id)}
                onMouseLeave={() => setOpenPopover(null)}
              >
                {item.label}
              </PopoverContent>
            </Popover>
          ))}
        </div>

        <div className="py-3 space-y-1 flex justify-center flex-col" data-dropdown-button>
          {navItems.slice(6).map((item) => {
            if (item.id === "profile") {
              return (
                <Popover
                  key={item.id}
                  open={openPopover === item.id}
                  onOpenChange={(open) => setOpenPopover(open ? item.id : null)}
                >
                  <PopoverTrigger asChild>
                    <button
                      onMouseEnter={() => setOpenPopover(item.id)}
                      onMouseLeave={() => setOpenPopover(null)}
                      onClick={() => onSectionClick(item.id as NavSection)}
                      className={cn(
                        "w-full h-[48px] flex items-center justify-center text-slate-300 hover:text-white transition-colors",
                        activeSection === item.id && "text-white"
                      )}
                    >
                      <item.icon className="w-7 h-7" />
                    </button>
                  </PopoverTrigger>
                  <PopoverContent
                    side="right"
                    className="p-0 w-48 rounded-md shadow-md"
                    sideOffset={-12}
                    onMouseEnter={() => setOpenPopover(item.id)}
                    onMouseLeave={() => setOpenPopover(null)}
                  >
                    <div className="py-2 text-sm">
                      {isSysAdmin && (
                        <a
                          href="/manage"
                          className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer"
                        >
                          <MdOutlineManageAccounts className="w-6 h-6" />
                          Manage
                        </a>
                      )}
                      <a
                        href="/profile"
                        className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer"
                      >
                        <TbUserCircle className="w-6 h-6" />
                        View Profile
                      </a>
                      <button
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent triggering other click handlers
                          handleLogout();
                        }}
                        className="flex items-center gap-2 w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
                      >
                        <TbLogout className="w-6 h-6" />
                        Logout
                      </button>
                    </div>
                  </PopoverContent>
                </Popover>
              );
            }

            if (item.id === "notifications") {
              return (
                <Popover
                  key={item.id}
                  open={openPopover === item.id}
                  onOpenChange={(open) => setOpenPopover(open ? item.id : null)}
                >
                  <PopoverTrigger asChild>
                    <div
                      className="w-full h-[48px] flex items-center justify-center"
                      onMouseEnter={() => setOpenPopover(item.id)}
                      onMouseLeave={() => setOpenPopover(null)}
                    >
                      <item.icon />
                    </div>
                  </PopoverTrigger>
                  <PopoverContent
                    side="right"
                    className="w-auto py-2 px-4 text-lg font-medium"
                    sideOffset={-12}
                    onMouseEnter={() => setOpenPopover(item.id)}
                    onMouseLeave={() => setOpenPopover(null)}
                  >
                    {item.label}
                  </PopoverContent>
                </Popover>
              );
            }

            return (
              <Popover
                key={item.id}
                open={openPopover === item.id}
                onOpenChange={(open) => setOpenPopover(open ? item.id : null)}
              >
                <PopoverTrigger asChild>
                  <button
                    onMouseEnter={() => setOpenPopover(item.id)}
                    onMouseLeave={() => setOpenPopover(null)}
                    onClick={() => onSectionClick(item.id as NavSection)}
                    className={cn(
                      "w-full h-[48px] flex items-center justify-center text-slate-300 hover:text-white transition-colors",
                      activeSection === item.id && "text-white"
                    )}
                  >
                    <item.icon className="w-7 h-7" />
                  </button>
                </PopoverTrigger>
                <PopoverContent
                  side="right"
                  className="w-auto py-2 px-4 text-lg font-medium"
                  sideOffset={-12}
                  onMouseEnter={() => setOpenPopover(item.id)}
                  onMouseLeave={() => setOpenPopover(null)}
                >
                  {item.label}
                </PopoverContent>
              </Popover>
            );
          })}
        </div>
      </nav>
    </div>
  );
}
