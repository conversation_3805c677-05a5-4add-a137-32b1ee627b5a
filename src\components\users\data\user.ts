// data/users.ts
export interface User {
    id: string;
    fullName: string;
    username: string;
    email: string;
    departments: string[];
  }
  
  export const dummyUsers: User[] = [
    {
      id: "1",
      fullName: "<PERSON>",
      username: "alicej",
      email: "<EMAIL>",
      departments: ["Engineering", "QA"],
    },
    {
      id: "2",
      fullName: "<PERSON>",
      username: "bob<PERSON>",
      email: "<EMAIL>",
      departments: ["Design"],
    },
    {
      id: "3",
      fullName: "<PERSON>",
      username: "caroln",
      email: "<EMAIL>",
      departments: ["Marketing", "Sales"],
    },
    {
      id: "4",
      fullName: "<PERSON>",
      username: "david<PERSON>",
      email: "<EMAIL>",
      departments: ["Support"],
    },
    {
      id: "5",
      fullName: "<PERSON>",
      username: "evap",
      email: "<EMAIL>",
      departments: ["HR", "Finance"],
    },
  ];
  