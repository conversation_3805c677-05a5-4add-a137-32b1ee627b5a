import { ApiResponse, http } from "./methods";
import { NotificationCountResponse, NotificationResponse } from "./types/types";

export const getNotifications = async (query: {
  page?: number;
  limit?: number;
}): Promise<NotificationResponse> => {
  const { page = 1, limit = 10 } = query;
  const params = new URLSearchParams();
  if (page) params.append("page", String(page));
  if (limit) params.append("limit", String(limit));
  try {
    const url = `/notifications?${params.toString()}`;
    const response = await http.get<NotificationResponse>(url);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getNotificationsCount =
  async (): Promise<NotificationCountResponse> => {
    try {
      const url = `/notifications/count`;
      const response = await http.get<NotificationCountResponse>(url);
      return response.data;
    } catch (error) {
      throw error;
    }
  };

export const markNotificationRead = async (
  notificationId: string
): Promise<ApiResponse> => {
  try {
    const url = `/notifications/${notificationId}/read`;
    const response = await http.put<ApiResponse>(url);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const markAllNotificationRead = async (): Promise<ApiResponse> => {
  try {
    const url = `/notifications/read-all`;
    const response = await http.put<ApiResponse>(url);
    return response.data;
  } catch (error) {
    throw error;
  }
};
