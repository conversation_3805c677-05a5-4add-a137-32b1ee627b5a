"use client"

import { Info, Plus } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button/button"
import ActionSection from "./components/ActionSection"
import DataAssetsSection from "./components/DataAssetsSection"
import ConnectionsSection from "./components/ConnectionsSection"
import ExploreWorldSection from "./components/ExploreWorldSection"

export default function Component() {
  return (
    <div className="w-full h-full bg-[#fafcff]">
      {/* Main Content */}
      <div className="w-full h-full flex flex-col">
        {/* Header */}
        <div className="bg-white p-6 pb-0">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-semibold text-[#333333]">Data Overview</h1>
              <p className="text-[#666666] text-sm mt-1">
                Easily track your data connection errors, data status, resolve issues, view requirements, and upload
                necessary documents.
              </p>
            </div>
            <Button className="bg-[#00b2a1] hover:bg-[#018e42] text-white">
              <Plus className="w-4 h-4 mr-2" />
              Upload Data
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {/* Info Banner */}
          <div className="rounded-lg pb-3 mb-1 flex justify-start items-center gap-3">
            <Info className="w-5 h-5" />
            <p className="text-[#666666] text-sm">
              It may take some time for data to complete the Sutra AI data unification process and be viewable in the
              dashboard. If you don't see assets you have uploaded, please check back later.
            </p>
          </div>

          <ActionSection />
          <DataAssetsSection />
          <ConnectionsSection />
          <ExploreWorldSection />
        </div>
      </div>
    </div>
  )
}
