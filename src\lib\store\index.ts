// store/index.ts
import { configureStore } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import rootReducer from "./rootReducer";
import { checkAuthExpiration } from "@/lib/store/features/user/userSlice";

const persistConfig = {
  key: "root",
  storage,
  whitelist: [
    "user",
    "tab",
    "favorites",
    "assetsTab",
    "manageTab",
    "README",
    "Changelog",
    "runInBg",
    "sidebar",
    "activeLink",
  ],
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export const persistor = persistStore(store);

// Dispatch checkAuthExpiration after rehydration
persistor.subscribe(() => {
  const { bootstrapped } = persistor.getState();
  if (bootstrapped) {
    store.dispatch(checkAuthExpiration());
  }
});

// Infer types for dispatch and state
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
