"use client";
import { IntercomProvider, useIntercom } from "react-use-intercom";
import { useEffect, useState } from "react";
import CryptoJS from "crypto-js";
import { useDispatch, useSelector } from 'react-redux';
import { closeIntercom, openIntercom } from '@/lib/store/features/intercom/intercomSlice';
import { RootState } from '@/lib/store';

const INTERCOM_APP_ID = process.env.NEXT_PUBLIC_INTERCOM_APP_ID || "";
const SECRET_KEY = process.env.NEXT_PUBLIC_INTERCOM_SECRET_KEY || "";


export default function IntercomWrapper({ children }: { children: React.ReactNode }) {
    const [user, setUser] = useState<{ username: string; email: string; userHash: string } | null>(null);
    const dispatch = useDispatch();
    useEffect(() => {
        try {
            const storedUser: any = JSON.parse(localStorage.getItem("user") || "{}");
            if (!storedUser.user) return;
            const username = storedUser.user.name.trim();

            const email = storedUser.user.email;
            const userHash = CryptoJS.HmacSHA256(username, SECRET_KEY).toString(CryptoJS.enc.Hex);

            setUser({ username: username.trim(), email, userHash });
        } catch (error) {
            //console.error("Error parsing user data:", error);
        }
    }, []);

    return (
        <IntercomProvider appId={INTERCOM_APP_ID} autoBoot={false} onHide={() => dispatch(closeIntercom())} onShow={() => dispatch(openIntercom())} >
            <IntercomWrapperWithUserData user={user}>{children}</IntercomWrapperWithUserData>
        </IntercomProvider>
    );
}

interface IntercomWrapperProps {
    user: { username: string; email: string; userHash: string } | null;
    children: React.ReactNode;
}

const IntercomWrapperWithUserData = ({ user, children }: IntercomWrapperProps) => {
    const { boot, show, hide } = useIntercom();
    const dispatch = useDispatch();
    const isOpen = useSelector((state: RootState) => state.intercom.isOpen);
    const [isBooted, setIsBooted] = useState(false);

    useEffect(() => {
        if (user) {
            boot({
                userId: user.username,
                email: user.email,
                name: user.username,
                userHash: user.userHash,
            });
            setIsBooted(true);
        }
    }, [user, boot]);

    useEffect(() => {
        if (!isBooted) return;

        if (isOpen) {
            show();
        } else {
            hide();
        }
    }, [isOpen, show, hide, isBooted]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const intercomElement = document.querySelector('#intercom-container');
            if (intercomElement && !intercomElement.contains(event.target as Node)) {
                dispatch(closeIntercom());
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [dispatch]);

    return <>{children}</>;
};
