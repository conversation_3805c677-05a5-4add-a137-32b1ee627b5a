"use client"

import React from "react"

interface DataProgressBarProps {
  totalFiles: number
  inProcessFiles: number
  silverFiles: number
  goldFiles: number
  aiReadyInUseFiles: number
  className?: string
}

export default function DataProgressBar({
  totalFiles,
  inProcessFiles,
  silverFiles,
  goldFiles,
  aiReadyInUseFiles,
  className = ""
}: DataProgressBarProps) {
  // Calculate percentages
  const inProcessPercentage = totalFiles > 0 ? (inProcessFiles / totalFiles) * 100 : 0
  const silverPercentage = totalFiles > 0 ? (silverFiles / totalFiles) * 100 : 0
  const goldPercentage = totalFiles > 0 ? (goldFiles / totalFiles) * 100 : 0
  const aiReadyInUsePercentage = totalFiles > 0 ? (aiReadyInUseFiles / totalFiles) * 100 : 0

  return (
    <div className={`w-full ${className}`}>
      {/* Progress Bar */}
      <div className="relative w-full h-2 bg-gray-200 rounded-full overflow-hidden">
        {/* Blue segment for In Process + Silver + Gold */}
        <div
          className="absolute left-0 top-0 h-full bg-[#3c82f6] transition-all duration-300"
          style={{
            width: `${inProcessPercentage + silverPercentage + goldPercentage}%`
          }}
        />
        
        {/* Purple/Pink segment for AI Ready In-Use */}
        <div
          className="absolute top-0 h-full bg-[#a855f7] transition-all duration-300"
          style={{
            left: `${inProcessPercentage + silverPercentage + goldPercentage}%`,
            width: `${aiReadyInUsePercentage}%`
          }}
        />
      </div>
      
      {/* Progress Text */}
      <div className="flex justify-end mt-2">
        <p className="text-xs text-[#666666]">
          {Math.round(aiReadyInUsePercentage)}% ({aiReadyInUseFiles} files) AI Ready Data In-Use
        </p>
      </div>
    </div>
  )
}
