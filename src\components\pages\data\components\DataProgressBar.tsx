"use client"

import React from "react"

interface DataProgressBarProps {
  totalFiles: number
  inProcessFiles: number
  silverFiles: number
  goldFiles: number
  aiReadyInUseFiles: number
  className?: string
}

export default function DataProgressBar({
  totalFiles,
  inProcessFiles,
  silverFiles,
  goldFiles,
  aiReadyInUseFiles,
  className = ""
}: DataProgressBarProps) {
  // Calculate percentages based on total files
  // AI Ready In-Use is a subset of Gold files, so we need to separate them
  const goldNotInUse = goldFiles - aiReadyInUseFiles
  const processedFiles = inProcessFiles + silverFiles + goldNotInUse
  const processedPercentage = totalFiles > 0 ? (processedFiles / totalFiles) * 100 : 0
  const aiReadyInUsePercentage = totalFiles > 0 ? (aiReadyInUseFiles / totalFiles) * 100 : 0

  return (
    <div className={`w-full ${className}`}>
      {/* Progress Bar */}
      <div className="relative w-full h-2 bg-gray-200 rounded-full overflow-hidden">
        {/* Blue segment for processed files (In Process + Silver + Gold) */}
        {processedPercentage > 0 && (
          <div
            className="absolute left-0 top-0 h-full bg-[#3c82f6] transition-all duration-300"
            style={{
              width: `${processedPercentage}%`
            }}
          />
        )}

        {/* Purple segment for AI Ready In-Use files */}
        {aiReadyInUsePercentage > 0 && (
          <div
            className="absolute top-0 h-full bg-[#a855f7] transition-all duration-300"
            style={{
              left: `${processedPercentage}%`,
              width: `${aiReadyInUsePercentage}%`
            }}
          />
        )}
      </div>

      {/* Progress Text */}
      <div className="flex justify-end mt-2">
        <p className="text-xs text-[#666666]">
          {Math.round(aiReadyInUsePercentage)}% ({aiReadyInUseFiles} files) AI Ready Data In-Use
        </p>
      </div>
    </div>
  )
}
