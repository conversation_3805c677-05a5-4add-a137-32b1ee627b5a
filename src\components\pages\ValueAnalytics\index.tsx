"use client"

import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { motion } from "framer-motion"
import { fetchAllValueAnalytics, fetchFilteredValueAnalytics } from "@/lib/store/features/va/valueAnalyticsSlice"
import { fetchValuePillars } from "@/lib/store/features/va/valuePillarsSlice"
import type { RootState, AppDispatch } from "@/lib/store"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ProjectSelector } from "./project-selector"
import { DashboardOverview } from "./dashboard-overview"
import { DashboardBreakdown } from "./dashboard-breakdown"
import { ValuePillarsOverall } from "./value-pillars-overall"
import getValueAnalyticsData from "@/service/valueAnalytics"
import Loader from "@/components/common/loader/loader"
import NoDataPage from "./no-data-page"

export default function ValueAnalytics() {
  const dispatch = useDispatch<AppDispatch>();
  const [timeRange, setTimeRange] = useState("ALL")
  const [selectedProjectId, setSelectedProjectId] = useState<string>("all")
  const [error, setError] = useState<string | null>(null);

  const { projects, monthlyAggregation, filteredData, status } = useSelector((state: RootState) => state.valueAnalytics);
  const isLoading = status === 'loading';

  useEffect(() => {
    setError(null); // Reset error on mount
    dispatch(fetchAllValueAnalytics()).catch((err: any) => {
      setError('Failed to load Value Analytics data.');
      console.error('Value Analytics API error:', err);
    });
  }, [dispatch]);

  useEffect(() => {
    if (timeRange === 'ALL' && selectedProjectId === 'all') {
      return;
    }

    const dateRange = getDateRange(timeRange);
    dispatch(fetchFilteredValueAnalytics({
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
      projectId: selectedProjectId === "all" ? undefined : selectedProjectId
    }));
  }, [timeRange, selectedProjectId, dispatch]);

  useEffect(() => {
    dispatch(fetchValuePillars()).then((action) => {
      if (action.payload) {
        localStorage.setItem("valuePillars", JSON.stringify(action.payload));
      }
    });
  }, [dispatch]);

  const currentData = (timeRange === 'ALL' && selectedProjectId === 'all')
    ? { projects, monthlyAggregation }
    : filteredData;

  const getDateRange = (range: string): { startDate: string | undefined; endDate: string | undefined } => {
    const today = new Date()
    let startDate: Date | undefined = undefined

    switch (range) {
      case 'LAST_MONTH': {
        startDate = new Date(today)
        startDate.setMonth(today.getMonth() - 1)
        break
      }
      case 'LAST_QUARTER': {
        startDate = new Date(today)
        startDate.setMonth(today.getMonth() - 3)
        break
      }
      case 'YEAR_TO_DATE': {
        startDate = new Date(today)
        startDate.setMonth(today.getMonth() - 12)
        break
      }
      default: {
        return { startDate: undefined, endDate: undefined }
      }
    }

    return {
      startDate: startDate.getTime().toString(),
      endDate: ""
    }
  }

  const getFilteredProjects = () => {
    return selectedProjectId === "all"
      ? currentData.projects
      : currentData.projects.filter((project: any) => project.id === selectedProjectId);
  };

  const calculateAggregatedData = () => {
    let hardValue = 0;
    let softValue = 0;

    const projectsToCalculate = getFilteredProjects();

    projectsToCalculate.forEach((project: any) => {
      project.valuePillars.forEach((pillar: any) => {
        pillar.benefits.forEach((benefit: any) => {
          benefit.metrics.forEach((metric: any) => {
            if (metric.dollarValue) {
              if (metric.dollarType === 'Hard') {
                hardValue += metric.dollarValue;
              } else if (metric.dollarType === 'Soft') {
                softValue += metric.dollarValue;
              }
            }
          });
        });
      });
    });

    const totalValue = hardValue + softValue;
    const totalInvestment = 162500;

    return {
      totalInvestment,
      totalValue,
      hardValue,
      softValue,
      projectsCount: projectsToCalculate.length,
      monthlyAggregation: currentData.monthlyAggregation
    };
  };

  const aggregatedData = calculateAggregatedData();

  const handleProjectSelect = (projectId: string) => {
    setSelectedProjectId(projectId);
  };

  // Check if there's no data
  const hasNoData = !isLoading && projects.length === 0 && monthlyAggregation.length === 0;

  if (error) {
    return (
      <div className="p-6 text-red-600 bg-red-50 rounded">
        <h2 className="text-xl font-bold mb-2">Error</h2>
        <p>{error}</p>
        <p className="text-sm mt-2">Please try refreshing the page or contact support if the problem persists.</p>
      </div>
    );
  }

  if (hasNoData) {
    return <NoDataPage />;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="p-6 space-y-6 min-h-screen"
    >
      {isLoading ? <Loader /> : (
        <>
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Value Analytics</h1>
            </div>
            <div className="flex space-x-4">
              <ProjectSelector
                projects={projects}
                selectedProjectId={selectedProjectId}
                onSelectProject={handleProjectSelect}
              />
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Time</SelectItem>
                  <SelectItem value="LAST_MONTH">Last Month</SelectItem>
                  <SelectItem value="LAST_QUARTER">Last Quarter</SelectItem>
                  <SelectItem value="YEAR_TO_DATE">Year To Date</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DashboardOverview {...aggregatedData} />
          <ValuePillarsOverall projects={getFilteredProjects()} />
          <DashboardBreakdown projects={getFilteredProjects()} />
        </>
      )}
    </motion.div>
  )
}






