import * as React from "react"
import { cn } from "@/lib/utils"

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<"input">>(
  ({ className, type = "text", ...props }, ref) => {
    return (
      <div className="relative w-full">
        <div className="absolute inset-y-0 left-3  flex items-center">
          <img
            src="/deployed-search.png"
            alt="Search"
            className="w-5 h-5"
          />
        </div>

        <input
          type={type}
          className={cn(
            "h-10 w-full rounded-[4px]  border-gray-300 bg-[#F4F5F6] pl-10 pr-4 py-2 text-base placeholder-gray-400 focus:outline-none focus:ring-[0.5px] focus:ring-[#00B2A1] focus:border-[#00B2A1] md:text-md ",
            className
          )}
          ref={ref}
          placeholder="Search Project..."
          {...props}
        />
      </div>
    )
  }
)

Input.displayName = "Input"

export { Input }
