import { Search } from "lucide-react"
import { useDispatch, useSelector } from "react-redux"
import { useState, useRef, useEffect } from "react"
import { useRouter,usePathname, useSearchParams } from "next/navigation"
import { RootState } from "@/lib/store"
import { setCurrentQuery } from "@/lib/store/features/search/searchSlice"

export default function SearchBar() {
  const isOpen = useSelector((state: RootState) => state.sidebar.isOpen)
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()
  const currentQuery = useSelector((state: RootState) => state.search.currentQuery);
  const [query, setQuery] = useState(currentQuery)
  const dispatch = useDispatch()
  const pathname = usePathname();
  const searchParams = useSearchParams()

  useEffect(() => {
    dispatch(setCurrentQuery(query))
  },[query])

  const handleSubmit = (searchQuery: string) => {
    setShowSuggestions(false)
    if (searchQuery.trim()) {
      const storedQueries: string[] = JSON.parse(
        localStorage.getItem("searchQueries") || "[]"
      )
      if (!storedQueries.includes(searchQuery)) {
        storedQueries.push(searchQuery)
        localStorage.setItem("searchQueries", JSON.stringify(storedQueries))
      }
      router.push(`/search?query=${encodeURIComponent(searchQuery)}`)
    }
  }

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    handleSubmit(query)
  }

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion)
    handleSubmit(suggestion)
    setShowSuggestions(false)
  }

  const handleInputFocus = () => {
    setIsFocused(true)
  }

  const handleInputBlur = () => {
    setIsFocused(false)
  }

  useEffect(() => {
    if (query && isFocused) {
      const storedQueries: string[] = JSON.parse(
        localStorage.getItem("searchQueries") || "[]"
      )
      const filteredSuggestions = storedQueries
        .filter((item) => item.toLowerCase().includes(query.toLowerCase()))
        .slice(0, 5)
      setSuggestions(filteredSuggestions)
      setShowSuggestions(true)
    } else {
      setSuggestions([])
      setShowSuggestions(false)
    }
  }, [query, isFocused])

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  useEffect(() => {
    if (pathname === "/search") {
      const queryParam = searchParams.get("query") || "";
      setQuery(queryParam);
    }
    setShowSuggestions(false);
  }, [searchParams, pathname]);

  useEffect(() => {
    if (pathname !== "/search") {
      setQuery(""); // Clear input when leaving search page
    }
    setShowSuggestions(false);
  }, [pathname]);

  return (
    <div 
      className={`relative top-0 transition-all duration-300 my-4 z-5 ${isOpen ? 'ml-[300px]' : 'ml-[140px]'} h-12 `}
      ref={containerRef}
    >
      <div 
        className={`flex justify-center items-center z-5 ${isOpen ? 'w-[calc(100%-300px)]' : 'w-[calc(100%-140px)]'} h-full`}
      >
        <div className="w-[600px] max-w-[90%] relative">
          <form onSubmit={handleFormSubmit}>
            <div className="flex items-center w-full rounded-full bg-white border border-gray-200 shadow-sm px-4 py-3">
              <Search className="h-5 w-5 text-gray-400 mr-2" />
              <input
                ref={inputRef}
                type="text"
                placeholder="Search for Data Asset, Projects and Documents..."
                className="flex-1 bg-transparent border-none outline-none text-gray-600 placeholder-gray-400 text-sm"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
              />
            </div>
          </form>

          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute w-full mt-2 bg-white rounded-lg border border-gray-200 shadow-lg overflow-hidden">
              <ul className="py-1">
                {suggestions.map((suggestion, index) => (
                  <li
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="flex items-center px-4 py-2 hover:bg-gray-50 cursor-pointer"
                  >
                    <Search className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-700">{suggestion}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}