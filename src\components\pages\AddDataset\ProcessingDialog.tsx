import React, { useEffect, useState } from "react";

interface ProcessingDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
}

const ProcessingDialog: React.FC<ProcessingDialogProps> = ({ isOpen, onClose, onComplete }) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const texts = [
    'Extracting Search Tags...',
    'Generating Ontology...',
    'Generating Summary...',
    'Identifying Use-cases...',
    'Identifying Data Industry...',
    'Identifying Geo Locations...'
  ];
  const [runinbgstate,setruninbgstate] = useState(false);

  useEffect(() => {
    if (!isOpen) return;

    // Reset the text index when dialog opens
    setCurrentTextIndex(0);

    // Create intervals for changing text
    const interval = setInterval(() => {
      setCurrentTextIndex(prevIndex => {
        // When we reach the last text, clear the interval and call onComplete
        if (prevIndex >= texts.length - 1) {
          clearInterval(interval);
          setruninbgstate(true);
          // We don't immediately close - we keep the last message displayed
          // onComplete will be called by the parent when ready to proceed
          return prevIndex;
        }
        return prevIndex + 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isOpen, texts.length]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white  p-6 rounded-lg shadow-lg w-95 h-95 flex flex-col items-center justify-between">
        <div className="mb-6">
          <img 
            src="/assets/process_animation.gif"
            alt="Processing Animation"
            className="w-32 h-32 "
          />
        </div>
        
        <h2 className="text-xl font-medium mb-2">Sit back and relax—exciting discoveries take time.</h2>
        <p className="text-gray-500 mb-4">Estimated time: 20 seconds approximately.</p>
        
        <div className="mb-4">
          <p className="text-gray-600">We are processing your data</p>
          <p className="text-black font-bold text-lg">{texts[currentTextIndex]}</p>
        </div>
        
        <button
          onClick={onClose}
          disabled={!runinbgstate}
          className={`px-4 py-2 ${runinbgstate?"text-teal-500 cursor-pointer":"text-gray-500 cursor-not-allowed"} rounded-md transition-colors`}
        >
          Run in Background
        </button>
      </div>
    </div>
  );
};

export default ProcessingDialog;