'use client';

import { useSelector } from 'react-redux';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect } from 'react';
import { selectIsAuthenticated } from '@/lib/store/features/user/selectors';

const AuthWrapper = ({ children }: { children: React.ReactNode }) => {
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!isAuthenticated) {
      // Store the complete URL including search parameters in localStorage before redirecting to login
      const fullUrl = window.location.pathname + window.location.search;
      localStorage.setItem('returnUrl', fullUrl);
      router.push('/login');
    }
  }, [isAuthenticated, router, pathname]);

  if (!isAuthenticated) {
    return null; // Optionally, render a loading indicator
  }

  return <div>{children}</div>;
};

export default AuthWrapper;
