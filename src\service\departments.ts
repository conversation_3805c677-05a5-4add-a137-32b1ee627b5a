import { http } from "./methods";

export interface Department {
  _id: string;
  name: string;
  description: string;
  thumbnailUrl: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface DepartmentsResponse {
  data: {
    departments: {
      departments: Department[];
      totalItems: number;
      totalPages: number;
      currentPage: number;
    };
  };
  status: number;
}

const departmentService = {
  getDepartmentList: async () => {
    const response = await http.get<any>("/departments");
    return response;
  },
  getAllDepartmentList: async () => {
    const response = await http.get<any>(
      "/departments/get-all-departments?page=1&limit=100"
    );
    return response;
  },
  getDepartmentById: async (id: string) => {
    const response = await http.get<any>(`/departments/${id}`);
    return response;
  },
  createDepartment: async (data: any) => {
    const response = await http.post<any>("/departments", data);
    return response;
  },
  updateDepartment: async (id: string, data: any) => {
    const response = await http.put<any>(`/departments/${id}`, data);
    return response;
  },
  deleteDepartment: async (id: string) => {
    const response = await http.delete<any>(`/departments/${id}`);
    return response;
  },
};

export default departmentService;
