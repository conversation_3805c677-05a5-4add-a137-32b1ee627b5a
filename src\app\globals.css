@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #000000;
  --sidebar-background: #3b4154;
  --sidebar-background-hover: #656d86;
  --sidebar-item-selected: #00b2a1;
  --sidebar-font-family: "Lato";
  --border-color: #cfd2de;
  --sidebar-fav-text: #00f5dc;
  --sidebar-border: #666f8f;
  --data-preview-tabs-selector-bg: #f4f5f6;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  font-family: var(--sidebar-font-family);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff;
    --foreground: #000000;
  }
}

body {
  color: var(--foreground);
  background: #eff3f8;
  font-family: var(--sidebar-font-family);
}

/* Custom scrollbar styles */
.sidebar-scroll-container {
  font-family: var(--sidebar-font-family);
  scrollbar-width: thin;
  scrollbar-color: #656d86 transparent;
}
.sidebar-scroll-container::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.sidebar-scroll-container::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-scroll-container::-webkit-scrollbar-thumb {
  background: #656d86;
  border-radius: 4px;
}

.sidebar-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #58607a;
}

.thin-border {
  border-bottom-width: 0.5px;
}

.first-row {
  border-top-width: 0.5px !important;
  border-top-color: #b0b0b0 !important;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for Firefox */
.scrollbar-hide {
  scrollbar-width: none;
}

.inputBox {
  background: rgba(255, 255, 255, 0);
  border-style: none;
  color: #f4f5f6;
  font-size: 16px;
}
