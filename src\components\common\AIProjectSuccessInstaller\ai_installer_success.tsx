import React from 'react';
import Image from 'next/image';


interface SuccessAIProjectLoaderProps {
    showModal: boolean;
    handleDone: () => void;
}

const AIInstallSuccessModal: React.FC<SuccessAIProjectLoaderProps> = ({
    showModal,
    handleDone
}) => {
    if (!showModal) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex justify-center items-center">
            <div className="bg-white p-6 rounded-[4px] shadow-lg max-w-sm w-full">
                <div className="flex justify-center items-center">
                    <Image
                        src="/assets/icons/ai_install_success.svg"
                        alt="Success Icon"
                        width={30}
                        height={30}
                    />
                </div>
                <div className="text-center text-lg mt-3 text-[#1B1D21]">
                    Project added successfully!
                </div>
                <div className="text-center text-sm mt-4 text-[#666F8F]">
                    To launch the project, go to “Deployed Projects”
                </div>
                <button
                    className="bg-[#00B2A1] text-white text-lg mt-10 mb-6 px-6 py-1 rounded mx-auto block"
                    onClick={handleDone}
                >
                    Done
                </button>
            </div>
        </div>

    );
};

export default AIInstallSuccessModal;
