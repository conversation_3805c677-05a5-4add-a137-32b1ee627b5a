"use client";

import React from "react";

type AboutAppModalProps = {
    onClose: () => void;
};

export default function FlowchartAIAbout({
    onClose,
}: AboutAppModalProps) {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg max-w-[80vw] w-full">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold text-gray-800">About Flowchart AI</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        ✕
                    </button>
                </div>
                <div className="px-6 py-10 text-gray-800" style={{ maxHeight: "70vh", overflowY: "auto" }}>
                    <div className="text-3xl font-bold mb-6">📊 Flowchart AI</div>

                    <section className="mb-10">
                        <h2 className="text-2xl font-semibold mb-2">Welcome to Flowchart AI!</h2>
                        <p>
                            <strong>Flowchart AI</strong> is a next-generation, AI-assisted visual thinking platform that empowers users to create, edit, and optimize flowcharts simply by typing a prompt. Designed for professionals, students, and teams alike, it combines natural language processing, intelligent suggestions, and a rich interactive canvas to streamline the entire flowchart creation and refinement process.
                        </p>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">🔍 Core Features</h2>
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-semibold">Prompt-Based Flowchart Generation</h3>
                                <p>
                                    Instantly turn plain text instructions or ideas into structured flowcharts. The AI automatically breaks down steps, decisions, and loops into nodes and connectors.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Smart Suggestions & Auto-Optimization</h3>
                                <p>
                                    Get real-time AI suggestions to improve clarity, remove redundancies, and add missing logic. Highlights areas for optimization such as bottlenecks, infinite loops, and unreachable endpoints.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Interactive Canvas</h3>
                                <p>
                                    Enjoy a drag-and-drop interface for easy node positioning and layout adjustment. Customize with colors, shapes, icons, and labels to tailor your diagrams visually.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Advanced Capabilities</h3>
                                <p>
                                    Convert flowcharts to code, sync documentation, and choose from different AI personas for tailored suggestions. Export in multiple formats including SVG, PNG, PDF, and more.
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">💼 Use Cases</h2>
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-semibold">Product Development</h3>
                                <p>
                                    Map out MVP workflows with auto-validation and optimization suggestions.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Business Operations</h3>
                                <p>
                                    Visualize and optimize internal SOPs with AI-powered insights.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Education</h3>
                                <p>
                                    Teach algorithms, logic gates, and decision trees interactively.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Marketing Automation</h3>
                                <p>
                                    Create and fine-tune campaign funnels with intelligent suggestions.
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">🚀 Why Choose Flowchart AI?</h2>
                        <ul className="list-disc list-inside space-y-2">
                            <li>No need to learn complex design tools or flowchart languages</li>
                            <li>Rapid ideation-to-implementation cycle with AI assistance</li>
                            <li>Perfect for visual thinkers who want logic-driven feedback</li>
                            <li>Seamless integration with popular tools and platforms</li>
                            <li>Real-time collaboration and version control</li>
                        </ul>
                    </section>

                    <p className="text-lg font-semibold">Start creating intelligent flowcharts with AI assistance today!</p>
                </div>
                <div className="mt-6 flex justify-end">
                    <button
                        onClick={onClose}
                        className="bg-[#00B2A1] text-white px-4 py-2 rounded-md hover:bg-[#00A090]"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
} 