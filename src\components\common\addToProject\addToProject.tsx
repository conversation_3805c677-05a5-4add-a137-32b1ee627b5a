import { Toast } from "@/components/ui/toast/toast";
import { addDataAssets } from "@/service/projects";
import React, { useState } from "react";
// import Loader from "../loader/loader";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { setActiveAssetTab } from "@/lib/store/features/project/projectAssetsSlice";
import { Loader, Loader2 } from "lucide-react";

interface AddToProjectInterface {
  projectId: string | null;
  datasetId: string | null;
  isInProject: boolean;
  fetchAllAssets?: () => void;
}

type ToastState = {
  message: string;
  type: "success" | "error";
} | null;

const AddToProject: React.FC<AddToProjectInterface> = ({
  projectId,
  datasetId,
  isInProject,
  fetchAllAssets,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [toast, setToast] = useState<ToastState>(null);
  const [loading, setLoading] = useState(false);
  const [isAdded, setIsAdded] = useState(isInProject);

  const addDataSetToProject = async () => {
    try {
      setLoading(true);
      if (projectId && datasetId) {
        const res = await addDataAssets({ projectId, datasetId });
        if (res.status == 201) {
          setIsAdded(true);
          setToast({
            message: "Asset added successfully.",
            type: "success",
          });
          // setTimeout(() => {
          //   dispatch(setActiveAssetTab("Project Assets"));
          //   router.push(`/projects/dashboard/${projectId}`);
          // }, 2000);
          //if (fetchAllAssets) fetchAllAssets();
        } else {
          setToast({ message: "Something went wrong!", type: "error" });
        }
      } else {
        setToast({ message: "Something went wrong!", type: "error" });
      }
    } catch (error: any) {
      setToast({ message: error.message, type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const handleClick = () => {
    addDataSetToProject();
  };

  return (
    <div>
      <button
        className={`flex gap-1 justify-center items-center rounded-[4px] px-3 py-[10px] text-md w-[140px]
    ${
      isAdded
        ? "bg-gray-300 text-gray-700 cursor-not-allowed"
        : "bg-teal-500 text-white"
    }
    `}
        onClick={handleClick}
        disabled={isInProject}
      >
        {!isAdded && !loading && (
          <span className="text-sm">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="size-5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m19.5 4.5-15 15m0 0h11.25m-11.25 0V8.25"
              />
            </svg>
          </span>
        )}

        {loading ? (
          <span className="flex justify-center w-full">
            <Loader2 className="w-5 h-5 animate-spin" />
          </span>
        ) : (
          <span className="flex gap-1 items-center">
            {isAdded ? (
              <>
                <span>Added</span>
                <span>to</span>
                <span>Project</span>
              </>
            ) : (
              <>
                <span>Add</span>
                <span>to</span>
                <span>Project</span>
              </>
            )}
          </span>
        )}
      </button>

      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export default AddToProject;
