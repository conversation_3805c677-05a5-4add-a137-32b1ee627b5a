import React from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { Project } from "@/types/value-analytics"

interface ProjectSelectorProps {
  projects: Project[]
  selectedProjectId: string
  onSelectProject: (projectId: string) => void
  disabled?: boolean
}

export function ProjectSelector({ 
  projects, 
  selectedProjectId, 
  onSelectProject,
  disabled = false
}: ProjectSelectorProps) {
  return (
    <Select value={selectedProjectId} onValueChange={onSelectProject} disabled={disabled}>
      <SelectTrigger className="w-[200px]">
        <SelectValue placeholder="Select a project" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Projects</SelectItem>
        {projects.map((project) => (
          <SelectItem key={project.id} value={project.id}>
            {project.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
