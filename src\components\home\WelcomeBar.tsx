"use client";
import { useState, useEffect } from "react";
import { ProgressPhase } from "./ProgressPhase";
import { useSelector } from "react-redux";


export function WelcomeBar() {
  const [timeOfDay, setTimeOfDay] = useState("");
  const [currentDate, setCurrentDate] = useState("");
  const userInfo = useSelector((state: any) => state.user.userInfo);

  useEffect(() => {
    const now = new Date();
    const hours = now.getHours();
    if (hours < 12) setTimeOfDay("Morning");
    else if (hours < 17) setTimeOfDay("Afternoon");
    else setTimeOfDay("Evening");
    setCurrentDate(
      now.toLocaleDateString("en-US", {
        weekday: "long",
        month: "long",
        day: "numeric",
      })
    );
  }, []);

  return (
    <div className="bg-white rounded-t-xl px-8 py-6">
      <div className="flex flex-col md:flex-row md:items-center w-full gap-2">
        {/* Left: Greeting and Date */}
        <div className="flex flex-col justify-center items-start w-full md:w-[38%]">
          <h1 className="text-3xl font-bold text-gray-900 leading-tight">
            Good {timeOfDay}, <span className="mr-1">{userInfo?.user?.username ? userInfo.user.username.charAt(0).toUpperCase() + userInfo.user.username.slice(1) : "User"}</span>
          </h1>
          <p className="text-sm text-gray-500 mt-1">Data as of: {currentDate}</p>
        </div>
        {/* Right: Progress Bar */}
        <div className="flex-1 w-full md:w-[62%] flex justify-end mt-4 md:mt-0">
          <div className="max-w-[600px] w-full">
            <ProgressPhase />
          </div>
        </div>
      </div>
    </div>
  );
}