import {
  DataAssetsResponse,
  GetDataAssetsResponse,
  ProjectDetails,
} from "@/service/types/types";
import React, { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { Pagination } from "@/components/common/pagination/pagination";
import { DatasetResponse } from "@/types/dataset";
import { MagnifyingGlassIcon } from "@heroicons/react/24/solid";
import Loader from "@/components/common/loader/loader";
import Tile from "@/components/common/tile/tile";
import AddDataset from "../../AddDataset/AddDataset";
import { getDataAssets } from "@/service/projects";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";

const ProjectAssetsTab = ({
  projectDetails,
}: {
  projectDetails: ProjectDetails | null;
}) => {
  const activeTab = useSelector(
    (state: RootState) => state.assetTab.activeAssetTab
  );
  const { id } = useParams() as { id: string };
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [allAssets, setAllAssets] = useState<GetDataAssetsResponse | null>(
    null
  );
  const [query, setQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [urlQuery, setUrlQuery] = useState({
    projectId: id,
    page: 1,
    limit: 10,
    search: "",
  });

  const fetchProjectAssets = async () => {
    try {
      setLoading(true);
      const res = await getDataAssets(urlQuery);
      setAllAssets(res);
    } catch (error) {
      // console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setUrlQuery((prev) => ({
      ...prev,
      search: query,
    }));
  };

  const handleViewDataset = (id: string) => {
    if (projectDetails)
      router.push(
        `/projects/dashboard/${encodeURIComponent(
          projectDetails._id
        )}/dataset/${id}?projectId=${encodeURIComponent(
          projectDetails._id
        )}`
      );
  };

  useEffect(() => {
    setUrlQuery((prev) => ({
      ...prev,
      page: currentPage,
    }));
  }, [currentPage]);

  useEffect(() => {
    fetchProjectAssets();
  }, [urlQuery]);

  return (
    <div className="font-[Lato] pt-5 pl-5 h-full">
      {loading ? (
        <Loader />
      ) : (
        <>
          <div>
            <h1 className="text-[#3B4154] text-xl font-[lato] font-semibold">
              Project Assets
            </h1>
            <div className="flex gap-2 items-center mt-2">
              <form
                onSubmit={handleSubmit}
                className="flex items-center w-3/4 h-full"
              >
                <input
                  type="text"
                  placeholder="Search Datasets"
                  className="w-full h-full bg-[#F4F5F6] text-[#3B4154] border rounded-l-[4px] py-3 p-2 outline-none placeholder-[#3B4154]"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                />
                <button
                  type="submit"
                  className="bg-teal-500 text-white py-3 px-2 rounded-r-[4px] cursor-pointer flex items-center justify-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth="1.5"
                    stroke="currentColor"
                    className="w-5 h-5"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 1 0 4.5 4.5a7.5 7.5 0 0 0 12.15 12.15z"
                    />
                  </svg>
                </button>
              </form>
              <button
                className="w-1/4 h-full py-2 border-[1px] border-teal-500 text-teal-500 rounded-md"
                onClick={() => setIsOpen(true)}
              >
                + Add Data Asset
              </button>
            </div>
          </div>
          <div className="flex flex-col gap-4 mt-4">
            <div>
              {allAssets?.assets.map((asset: any) => (
                <div key={asset._id}>
                  {asset?.isDataset ? (
                    <Tile
                      title={asset?.title}
                      description={asset?.description}
                      tags={asset?.tags}
                      button_text="View Dataset"
                      onButtonClick={() => handleViewDataset(asset?.datasetId)}
                    />
                  ) : (
                    <div className="mt-3">
                      <button className="flex gap-1 mb-1 bg-gray-200 rounded-md px-2 py-1 text-sm">
                        <img src="/assets/icons/ai-avatar-icon.svg" alt="#" />
                        <span className="font-semibold">Imported with AI</span>
                      </button>
                      <Tile
                        title={asset?.title}
                        description={asset?.description}
                        tags={asset?.tags}
                        // button_text="View Dataset"
                        onButtonClick={() => { }}
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
          <div className="flex justify-between items-center mt-3 pb-5">
            <div className="text-gray-600 text-sm">
              Showing {allAssets?.assets?.length || 0} of{" "}
              {allAssets?.total || 0}
            </div>
            {(allAssets?.total ?? 0) > 0 && (
              <Pagination
                totalPages={Math.ceil((allAssets?.total ?? 0) / 10)}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
              />
            )}
          </div>
        </>
      )}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity z-50"
          onClick={() => setIsOpen(false)}
        />
      )}
      <AddDataset
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        title={"Add Company Data"}
        isWorldDataset={false}
        projectDetails={projectDetails}
      />
    </div>
  );
};

export default ProjectAssetsTab;
