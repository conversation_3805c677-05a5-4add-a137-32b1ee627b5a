<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="257.688" height="111.605" viewBox="0 0 257.688 111.605">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_4465" data-name="Rectangle 4465" width="35.684" height="47.914" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <rect id="Rectangle_4467" data-name="Rectangle 4467" width="20.367" height="18.303" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-4">
      <rect id="Rectangle_4466" data-name="Rectangle 4466" width="20.367" height="18.297" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-5">
      <rect id="Rectangle_4469" data-name="Rectangle 4469" width="15.759" height="9.548" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-6">
      <rect id="Rectangle_4468" data-name="Rectangle 4468" width="15.758" height="9.54" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-7">
      <rect id="Rectangle_4471" data-name="Rectangle 4471" width="12.786" height="6.746" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-8">
      <rect id="Rectangle_4470" data-name="Rectangle 4470" width="12.781" height="6.745" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-9">
      <rect id="Rectangle_4473" data-name="Rectangle 4473" width="9.382" height="22.948" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-10">
      <rect id="Rectangle_4472" data-name="Rectangle 4472" width="9.382" height="22.946" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_5544" data-name="Group 5544" transform="translate(-624 -450)">
    <g id="Group_5247" data-name="Group 5247" transform="translate(624 450)">
      <rect id="Rectangle_4463" data-name="Rectangle 4463" width="257.688" height="37.028" rx="4" transform="translate(0 20.042)" fill="#f3f4f6"/>
      <path id="Path_10992" data-name="Path 10992" d="M280.115,129.668c-5.744,4.434-14.684,1.913-18.826-5.3l-5.1-8.884-3.562-6.212-9.834-17.148-.8-1.4,4.752-3.672,5.338-4.123,7.087,6.967,7.416,7.287h.014l6.4,6.287,6.751,6.629c5.937,5.827,6.113,15.123.367,19.559" transform="translate(-59.123 -20.262)" fill="#878eaa"/>
      <g id="Group_5226" data-name="Group 5226" transform="translate(189.507 62.669)" opacity="0.2" style="mix-blend-mode: multiply;isolation: isolate">
        <g id="Group_5225" data-name="Group 5225">
          <g id="Group_5224" data-name="Group 5224" clip-path="url(#clip-path)">
            <g id="Group_5223" data-name="Group 5223" transform="translate(0 0)">
              <g id="Group_5222" data-name="Group 5222" clip-path="url(#clip-path)">
                <path id="Path_10993" data-name="Path 10993" d="M282.261,129.667a10.2,10.2,0,0,1-1.921,1.178c4.106-4.787,3.429-12.836-1.916-18.084L250.776,85.6l3.454-2.668,27.662,27.177c5.938,5.828,6.113,15.123.369,19.557" transform="translate(-250.776 -82.931)" fill="#596391"/>
              </g>
            </g>
          </g>
        </g>
      </g>
      <g id="Group_5231" data-name="Group 5231" transform="translate(193.508 76.923)" style="mix-blend-mode: overlay;isolation: isolate">
        <g id="Group_5230" data-name="Group 5230">
          <g id="Group_5229" data-name="Group 5229" clip-path="url(#clip-path-3)">
            <g id="Group_5228" data-name="Group 5228" transform="translate(0 0)">
              <g id="Group_5227" data-name="Group 5227" clip-path="url(#clip-path-4)">
                <path id="Path_10994" data-name="Path 10994" d="M276.438,108.089A22.381,22.381,0,0,1,259.633,120.1l-3.561-6.212s11.379-2.7,13.95-12.087h.014Z" transform="translate(-256.071 -101.794)" fill="#b8bcdf"/>
              </g>
            </g>
          </g>
        </g>
      </g>
      <g id="Group_5236" data-name="Group 5236" transform="translate(183.672 62.662)" opacity="0.17" style="mix-blend-mode: multiply;isolation: isolate">
        <g id="Group_5235" data-name="Group 5235">
          <g id="Group_5234" data-name="Group 5234" clip-path="url(#clip-path-5)">
            <g id="Group_5233" data-name="Group 5233" transform="translate(0 0.007)">
              <g id="Group_5232" data-name="Group 5232" clip-path="url(#clip-path-6)">
                <path id="Path_10995" data-name="Path 10995" d="M258.814,89.287c-9.513,4.655-15.758,2.825-15.758,2.825l3.952-5.068,5.336-4.123,6.416,6.309Z" transform="translate(-243.055 -82.929)" fill="#596391"/>
              </g>
            </g>
          </g>
        </g>
      </g>
      <path id="Path_10996" data-name="Path 10996" d="M203.213.423a37.847,37.847,0,1,0,43.03,31.831h0A37.845,37.845,0,0,0,203.213.423M213.686,70.43a32.94,32.94,0,1,1,27.7-37.452,32.94,32.94,0,0,1-27.7,37.452h0" transform="translate(-41.769 0)" fill="#878eaa"/>
      <path id="Path_10997" data-name="Path 10997" d="M240.524,36.3a29.262,29.262,0,1,1-33.268-24.61h0A29.261,29.261,0,0,1,240.524,36.3" transform="translate(-44.544 -2.776)" fill="#e9eaef" opacity="0.77"/>
      <g id="Group_5241" data-name="Group 5241" transform="translate(152.169 12.447)" opacity="0.52">
        <g id="Group_5240" data-name="Group 5240">
          <g id="Group_5239" data-name="Group 5239" clip-path="url(#clip-path-7)">
            <g id="Group_5238" data-name="Group 5238" transform="translate(0.005)">
              <g id="Group_5237" data-name="Group 5237" clip-path="url(#clip-path-8)">
                <path id="Path_10998" data-name="Path 10998" d="M203.081,23.2a1.489,1.489,0,0,1-1.045-2.714,25.439,25.439,0,0,1,10.406-4,1.49,1.49,0,0,1,.441,2.947,22.493,22.493,0,0,0-9.2,3.531,1.467,1.467,0,0,1-.6.234" transform="translate(-201.371 -16.472)" fill="#fff"/>
              </g>
            </g>
          </g>
        </g>
      </g>
      <g id="Group_5246" data-name="Group 5246" transform="translate(141.372 19.971)" opacity="0.52">
        <g id="Group_5245" data-name="Group 5245">
          <g id="Group_5244" data-name="Group 5244" clip-path="url(#clip-path-9)">
            <g id="Group_5243" data-name="Group 5243">
              <g id="Group_5242" data-name="Group 5242" clip-path="url(#clip-path-10)">
                <path id="Path_10999" data-name="Path 10999" d="M189.057,49.361a1.488,1.488,0,0,1-1.693-1.251v0a25.64,25.64,0,0,1,6.512-21.19,1.489,1.489,0,1,1,2.191,2.017,22.661,22.661,0,0,0-5.758,18.737,1.491,1.491,0,0,1-1.254,1.693" transform="translate(-187.079 -26.43)" fill="#fff"/>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
