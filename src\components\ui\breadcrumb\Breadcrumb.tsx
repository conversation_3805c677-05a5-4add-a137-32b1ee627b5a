"use client";

import { setActiveLink } from "@/lib/store/features/activeModule/activeModuleSlice";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { useDispatch } from "react-redux";
import { useEffect, useState } from "react";
import { getProjectDetails } from "@/service/projects";

export default function Breadcrumb() {
  const pathname = usePathname() ?? "";
  const searchParams = useSearchParams();
  const nameFromQuery = searchParams.get("name");
  const dispatch = useDispatch();

  const pathParts = pathname.split("/").filter(Boolean);

  // State for project name
  const [projectName, setProjectName] = useState<string | null>(null);
  const [loadingProjectName, setLoadingProjectName] = useState(false);

  // Detect /projects/dashboard/[projectId] or /projects/console/[projectId] pattern
  useEffect(() => {
    const isDashboard =
      pathParts[0] === "projects" &&
      pathParts[1] === "dashboard" &&
      pathParts[2] && /^[a-f0-9]{24,}$/.test(pathParts[2]);
    const isConsole =
      pathParts[0] === "projects" &&
      pathParts[1] === "console" &&
      pathParts[2] && /^[a-f0-9]{24,}$/.test(pathParts[2]);
    if (isDashboard || isConsole) {
      setProjectName(null); // Clear previous name immediately
      setLoadingProjectName(true);
      getProjectDetails(pathParts[2])
        .then((res) => {
          setProjectName(res.data?.name || res.data?.project?.name || null);
        })
        .catch(() => setProjectName(null))
        .finally(() => setLoadingProjectName(false));
    } else {
      setProjectName(null);
      setLoadingProjectName(false);
    }
  }, [pathname]);

  // Don't show breadcrumb for short paths
  if (
    (pathParts.length < 3 && pathParts[0] !== "projects") ||
    (pathParts[0] === "projects" && pathParts.length === 1)
  )
    return null;

  const breadcrumbItems: { label: string; href: string }[] = [];
  let hrefParts: string[] = [];

  // Special case: /projects/dashboard/[projectId] or /projects/console/[projectId]
  const isDashboard =
    pathParts[0] === "projects" &&
    pathParts[1] === "dashboard" &&
    pathParts[2] && /^[a-f0-9]{24,}$/.test(pathParts[2]);
  const isConsole =
    pathParts[0] === "projects" &&
    pathParts[1] === "console" &&
    pathParts[2] && /^[a-f0-9]{24,}$/.test(pathParts[2]);
  if (isDashboard || isConsole) {
    if (loadingProjectName || !projectName) {
      // Show nothing while loading or if name is not available
      return null;
    }
    // Only show 'Projects' and project name
    breadcrumbItems.push({ label: "Projects", href: "/projects" });
    breadcrumbItems.push({ label: projectName, href: `/projects/${pathParts[1]}/${pathParts[2]}` });
  } else {
    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];
      const isId = /^[a-f0-9]{24,}$/.test(part);

      if (isId) {
        // Handle: /apps/deployed-projects/{id}?name=app2
        if (
          pathParts[i - 1] === "deployed-projects" &&
          pathParts[i - 2] === "apps" &&
          nameFromQuery
        ) {
          hrefParts.push(part);
          breadcrumbItems.push({
            label: nameFromQuery, // ✅ don't capitalize
            href: "/" + hrefParts.join("/"),
          });
          break;
        } else {
          hrefParts.push(part);
          if (breadcrumbItems.length > 0) {
            breadcrumbItems[breadcrumbItems.length - 1].href =
              "/" + hrefParts.join("/");
          }
          continue;
        }
      }

      // Skip "deployed-projects" if name is present
      if (part === "deployed-projects" && nameFromQuery) {
        hrefParts.push(part);
        continue;
      }

      hrefParts.push(part);

      // 🔥 Capitalize all labels except "nameFromQuery"
      const label =
        part == nameFromQuery
          ? nameFromQuery
          : part
            .replace(/-/g, " ")
            .split(" ")
            .map((word) =>
              word.toLowerCase() === "ai"
                ? "AI"
                : word.charAt(0).toUpperCase() + word.slice(1)
            )
            .join(" ");

      breadcrumbItems.push({
        label,
        href: "/" + hrefParts.join("/"),
      });
    }
  }

  return (
    <nav className="text-sm px-4 py-2">
      <ul className="flex flex-wrap items-center">
        {breadcrumbItems.map(({ label, href }, index) => {
          const isLast = index === breadcrumbItems.length - 1;
          if (index === 0 && label !== "Projects" && label !== "Apps")
            return null;
          return (
            <li key={href} className="flex items-center">
              {(index > 1 ||
                (index === 1 &&
                  (breadcrumbItems[0].label === "Projects" ||
                    breadcrumbItems[0].label === "Apps"))) && (
                  <span className="mx-1 text-gray-400 text-xl">{">"}</span>
                )}

              {isLast ? (
                <span className="text-gray-500 capitalize">{label}</span>
              ) : (
                <Link
                  href={href}
                  className="text-teal-500 hover:underline capitalize"
                  onClick={() => dispatch(setActiveLink(href))}
                >
                  {label}
                </Link>
              )}
            </li>
          );
        })}
      </ul>
    </nav>
  );
}
