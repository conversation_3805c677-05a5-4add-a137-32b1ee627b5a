import { useEffect, useRef, useState } from "react";
import { FaSearch, FaPlus } from "react-icons/fa";
import { useRouter } from "next/navigation";
import { getProjects } from "@/service/projects";
import { Project } from "@/service/types/types";
import Loader from "@/components/common/loader/loader";
import Image from "next/image";
import { useDispatch, useSelector } from "react-redux";
import { setProjectActiveTab } from "@/lib/store/features/project/projectTabSlice";
import { setActiveAssetTab } from "@/lib/store/features/project/projectAssetsSlice";
import { setActiveManageTab } from "@/lib/store/features/project/projectManageSlice";
import AIBoxSection from "./AIBoxSection/ai_box_section";
import { MdPerson } from "react-icons/md";

// Define status styles
const statusStyles: Record<string, { text: string; bg: string }> = {
  Draft: { text: "#FFFFFF", bg: "#888FAA" },
  Hold: { text: "#666F8F", bg: "#CFD2DE" },
  "In-process": { text: "#3B4154", bg: "#EAA23B" },
  Operational: { text: "#FFFFFF", bg: "#018E42" },
};

const ProjectList = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [search, setSearch] = useState("");
  const [projects, setProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const imgBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "";
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  const userInfo = useSelector((state: any) => state.user.userInfo);

  const hasAccess = (user: any): boolean => {
    if (user.user.isSysAdmin) {
      return true;
    } else {
      return user.user.department.some((department: { departmentRole: string; }) =>
        department.departmentRole === "admin" || department.departmentRole === "editor"
      );
    }
  };

  const showButton = hasAccess(userInfo);

  // Fetch projects from API
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const projectsData = await getProjects({
          page: 1,
          limit: 100,
        });
        // console.log(projectsData);
        setProjects(projectsData.projects);
        setFilteredProjects(projectsData.projects);
      } catch (err: any) {
        setError(err?.message || "Failed to fetch projects");
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = e.target.value;
    setSearch(searchTerm);

    if (searchTerm.trim() === "") {
      setFilteredProjects(projects);
    } else {
      const filtered = projects.filter((project) => project.name.toLowerCase().includes(searchTerm.toLowerCase()));
      setFilteredProjects(filtered);
    }
  };

  // Handle project selection
  const handleRowClick = (projectId: string) => {
    dispatch(setProjectActiveTab("Overview"));
    dispatch(setActiveAssetTab("All Assets"));
    dispatch(setActiveManageTab("General"));
    router.push(`/projects/dashboard/${projectId}`);
  };
  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo(0, 0);
    }
  }, [filteredProjects]);

  return (
    <div
      // className="min-h-screen "
      ref={scrollContainerRef}
      style={{
        backgroundColor: "var(--background)",
        color: "var(--foreground)",
        height: "calc(100vh - 6rem)",
        overflowY: "auto",
      }}
    >
      <h1 className="text-2xl mb-4 px-8 pt-4 ">Projects</h1>
      <AIBoxSection />
      <div className="px-8 py-4">
        <h2 className="font-semibold">Your Projects</h2>
        {projects.length > 0 && (
          <div className="flex w-full gap-5 h-10 mb-4 mt-4">
            <div className="relative w-full">
              <FaSearch className="absolute left-3 inset-y-3 flex items-center text-[#666F8F]" size={16} />
              <input
                type="text"
                placeholder="Search Projects"
                className="w-full pl-8 border border-[#CFD2DE] text-black px-3 rounded-[4px] bg-[#F4F5F6] 
                                placeholder-[#3B4154] focus:border-[#00B2A1] focus:ring-1 focus:ring-[#00B2A1] outline-none h-10"
                value={search}
                onChange={handleSearchChange}
              />
            </div>
            {showButton && projects.length > 0 && (
              <div
                onClick={() => router.push("/projects/add")}
                className="w-44 px-4 py-2 cursor-pointer rounded flex items-center justify-center"
                style={{ backgroundColor: "var(--sidebar-item-selected)", color: "#fff" }}
              >
                <FaPlus strokeWidth={0.5} size={14} className="mr-2" /> Add Project
              </div>
            )}
          </div>
        )}

        {loading ? (
          <div className="pt-10">
            <Loader />
          </div>
        ) : projects.length === 0 ? (
          <div className="flex flex-col text-gray-500 mt-4 justify-center items-center gap-4">
            <Image src="/assets/no_project_icon.svg" alt="No Projects" width={200} height={200} />
            <p> There are no projects listed at the moment.</p>
            {showButton && (
              <div
                className="mt-4 px-4 py-2 cursor-pointer rounded bg-[#00B2A1] text-white flex items-center justify-center"
                onClick={() => router.push("/projects/add")}
              >
                <FaPlus strokeWidth={0.5} size={14} className="mr-2" /> Add Project
              </div>
            )}

          </div>
        ) : (
          <>
            {filteredProjects.length === 0 ? (
              <p className="text-center text-gray-500 mt-4">No projects found for "{search}"</p>
            ) : (
              <div className="overflow-y-auto max-h-[600px]">
                <table className="w-full">
                  <thead className="sticky top-0 bg-white z-40">
                    <tr className="text-left font-semibold text-[#3B4154] text-sm border-b border-[#CFD2DE]">
                      <th className="px-2 py-4">Project Name</th>
                      <th className="px-2 py-4">Category</th>
                      <th className="px-2 py-4">Type</th>
                      <th className="px-2 py-4">Status</th>
                      <th className="px-2 py-4">Date Created</th>
                      <th className="px-2 py-4">Creator</th>
                      <th className="px-2 py-4">Collaborators</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredProjects.map((project) => (
                      <tr
                        key={project._id}
                        className="cursor-pointer text-sm border-b border-[#CFD2DE]"
                        onClick={() => handleRowClick(project._id)}
                      >
                        <td className="p-2">{project.name}</td>
                        <td className="p-2">{project.isAiBox ? "AI in a Box" : "Custom"}</td>
                        <td className="p-2">{project.appType === "Application" ? "App" : project.appType}</td>
                        <td className="p-2">
                          <span
                            className="inline-flex items-center px-5 rounded"
                            style={{
                              backgroundColor: statusStyles[project.status]?.bg || "#6B7280",
                              color: statusStyles[project.status]?.text || "#FFFFFF",
                            }}
                          >
                            <span
                              className="w-2 h-2 rounded-full mr-1 shrink-0"
                              style={{
                                backgroundColor: statusStyles[project.status]?.text || "#FFFFFF",
                              }}
                            ></span>
                            {project.status}
                          </span>
                        </td>
                        <td className="p-2">
                          {new Date(project.createdOn).toLocaleDateString("en-GB", {
                            day: "2-digit",
                            month: "short",
                            year: "numeric",
                          })}
                        </td>
                        <td className="p-2">{project.creator?.name ?? "Unknown"}</td>
                        <td className="p-2">
                          {project.collaborators.length > 0 ? (
                            <div className="relative flex -space-x-1 overflow-visible">
                              {project.collaborators.map((collab, i) => (
                                <div key={i} className="relative group ">
                                  {collab.userId && collab.userId.thumbnail ? (
                                    <img
                                      src={imgBaseUrl?.replace("/api", "") + collab.userId.thumbnail}
                                      alt={collab.userId.name}
                                      className="w-8 h-8 mx-1 rounded-full border-2 border-white "
                                    />
                                  ) : (
                                    <div className="mt-1">
                                      <MdPerson className="w-6 h-6 mx-1 rounded-full bg-slate-600" />
                                    </div>
                                  )}
                                  <span className="absolute left-1/2 -translate-x-1/2 bottom-full mb-1 px-2 py-1 text-xs text-black bg-white rounded-md opacity-0 transition-opacity duration-200 group-hover:opacity-100 z-50 whitespace-nowrap shadow-lg">
                                    {collab.userId?.name ?? "Unknown"}
                                  </span>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="p-1">
                              N/A
                            </div>

                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ProjectList;
