"use client";
import Filter from "@/components/common/filter/filter";
import Sort from "@/components/common/sort/sort";
import Tile from "@/components/common/tile/tile";
import { MagnifyingGlassIcon } from "@heroicons/react/24/solid";
import { use, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { dataService } from "@/service/dataService";
import Loader from "@/components/common/loader/loader";
import { Pagination } from "@/components/common/pagination/pagination";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { setImportActiveTab } from "@/lib/store/features/dataset/importSlice";
import { Import } from "lucide-react";
import Image from "next/image";

interface Filters {
  tags: string[];
  fileTypes: string[];
  licenses: string[];
}

export default function Projects() {
  const router = useRouter();
  const dispatch = useDispatch();
  const importActiveTabState = useSelector(
    (state: RootState) => state.importTab.importActiveTab
  );
  const [activeTab, setActiveTab] = useState(importActiveTabState);
  const [query, setQuery] = useState("");
  const [searchedQuery, setSearchedQuery] = useState("");
  const [isClearOpen, setIsClearOpen] = useState<boolean>(false);
  const [header, setHeader] = useState(false);
  const [isHidden, setisHidden] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortValue, setSortValue] = useState<string>("views");
  const [sortOrder, setSortOrder] = useState<number>(-1);
  const [selectedFilters, setSelectedFilters] = useState<Filters>({
    tags: [],
    fileTypes: [],
    licenses: [],
  });

  const [urlQuery, setUrlQuery] = useState({
    page: 1,
    limit: 10,
    search: "",
    tags: "",
    license: "",
    fileTypes: "",
    sortBy: sortValue,
    sortOrder: sortOrder,
  });

  useEffect(() => {
    dispatch(setImportActiveTab(activeTab));
  }, [activeTab]);

  useEffect(() => {
    setActiveTab(importActiveTabState);
  }, [importActiveTabState]);

  const [exploreUrlQuery, setExploreUrlQuery] = useState({
    page: 1,
    limit: 10,
    query: "",
  });

  const [data, setData] = useState<any>(null);
  const [exploreData, setExploreData] = useState<any>(null);
  const [openExplore, setOpenExplore] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleClearSearch = () => {
    setQuery("");
    setUrlQuery((prev) => ({
      ...prev,
      search: "",
    }));
    setExploreUrlQuery((prev) => ({
      ...prev,
      query: "",
    }));
    setIsClearOpen(false);
    setHeader(false);
    setisHidden(false);
    setOpenExplore(false);
    setExploreData(null);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setHeader(true);
    setisHidden(true);
    setSearchedQuery(query);
    setUrlQuery((prev) => ({
      ...prev,
      search: query,
    }));
    setExploreUrlQuery((prev) => ({
      ...prev,
      query: query,
    }));
    setOpenExplore(true);
    setIsClearOpen(true);
  };
  const handleViewDataset = (id: string) => {
    router.push(`/data/world-data/dataset/${id}`);
  };
  useEffect(() => {
    setUrlQuery((prev) => ({
      ...prev,
      tags: selectedFilters.tags.join(","), // Convert array to comma-separated string
      license: selectedFilters.licenses.join(","),
      fileTypes: selectedFilters.fileTypes.join(","),
    }));
  }, [selectedFilters]);

  useEffect(() => {
    setUrlQuery((prev) => ({
      ...prev,
      sortBy: sortValue,
      sortOrder: sortOrder,
    }));
  }, [sortValue, sortOrder]);

  useEffect(() => {
    if (activeTab === "Imported Data") {
      setUrlQuery((prev) => ({
        ...prev,
        page: currentPage,
      }));
    } else if (activeTab === "Explore world data") {
      setExploreUrlQuery((prev) => ({
        ...prev,
        page: currentPage,
      }));
    }
  }, [currentPage]);

  const hasFilters = Object.values(selectedFilters).some(
    (arr) => arr.length > 0
  );

  const removeFilter = (category: keyof Filters, value: string) => {
    setSelectedFilters((prevFilters) => ({
      ...prevFilters,
      [category]: prevFilters[category].filter((item) => item !== value),
    }));
  };

  const fetchImportedData = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await dataService.getImportedData(urlQuery);
      setData(result.data);
    } catch (err) {
      setError("Failed to fetch data.");
    } finally {
      setLoading(false);
    }
  };

  const fetchExploreData = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await dataService.getExploredData(exploreUrlQuery);
      setExploreData(result);
    } catch (err) {
      setError("Failed to fetch data.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (openExplore) fetchExploreData();
  }, [exploreUrlQuery]);

  useEffect(() => {
    fetchImportedData();
    // fetchExploreData();
  }, [urlQuery]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const NoDataPlaceholder = ({ message }: { message: string }) => (
    <div className="flex flex-col gap-2 justify-center items-center mt-20">
      <img
        src="/Group 5544.svg"
        alt="No data"
        className="w-36 h-36 text-gray-700 object-contain"
      />
      <h1 className="text-lg text-gray-400 mt-4">{message}</h1>
    </div>
  );
  return (
    <div className="p-8" style={{ fontFamily: "var(--Helvetica-font-family)" }}>
      <div className=" bg-white">
        <h1 className="text-[#333333] text-[24px] font-[500] mb-4">
          World Data
        </h1>
        <p className="mt-[15px] mb-2 text-[#666F8F] text-sm w-[450px]">
          Explore the world's largest public data repository containing over
          300,000 datasets that are pertinent to your needs.
        </p>
        <form
          onSubmit={(e) => handleSubmit(e)}
          className="flex items-center w-[701px] mb-4"
        >
          <input
            type="text"
            placeholder="Search datasets..."
            className="flex-1 h-[44px] bg-[#F4F5F6] text-[#3B4154] border  rounded-sm p-2 px-[10px] mt-2 outline-none placeholder-gray-500 focus:border-[#00B2A1] focus:outline-none "
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
          <button
            type="submit"
            className=" h-[44px] text-white bg-teal-500 p-3 mt-2 rounded-[4px] px-5 flex items-end justify-center text-[14px]"
          >
            Search
          </button>
        </form>
        {isHidden && (
          <div className="flex gap-2 items-center mt-4">
            {activeTab === "Explore world data" && (
              <h1 className=" text-[#3B4154] font-semibold text-xl">
                {exploreData?.worldData?.count} Datasets found for "
                {searchedQuery}"
              </h1>
            )}
            {activeTab === "Imported Data" && (
              <h1 className=" text-[#3B4154] font-semibold text-xl">
                {data?.pagination?.total} Datasets found for "{searchedQuery}"
              </h1>
            )}
            {isClearOpen && (
              <button
                className="text-md text-teal-500 bg-white"
                onClick={handleClearSearch}
              >
                Clear Search
              </button>
            )}
          </div>
        )}
        {hasFilters && activeTab === "Imported Data" && (
          <div className="flex flex-wrap gap-2 mt-6 rounded-lg items-center">
            {Object.entries(selectedFilters).map(([category, values]) => (
              <div key={category} className="flex gap-2 items-center">
                {values.length > 0 && (
                  <h1>
                    {category.charAt(0).toLocaleUpperCase() + category.slice(1)}
                    :{" "}
                  </h1>
                )}

                {values.map((value: any) => (
                  <div
                    key={`${category}-${value}`}
                    className="flex items-center gap-2 bg-[#F4F5F6] text-[#3B4154] px-3 py-1 rounded-md text-sm"
                  >
                    <div>{value}</div>
                    <button
                      onClick={() =>
                        removeFilter(category as keyof Filters, value)
                      }
                      className="  text-[#888FAA] text-sm"
                    >
                      ✖
                    </button>
                  </div>
                ))}
              </div>
            ))}
            <button
              className="text-md text-teal-500 bg-white"
              onClick={() =>
                setSelectedFilters({
                  tags: [],
                  fileTypes: [],
                  licenses: [],
                })
              }
            >
              Clear all
            </button>
          </div>
        )}

        <div className="flex justify-between items-center border-b mt-6">
          {/* Tab Navigation */}
          <div className="flex">
            {["Explore world data", "Imported Data"].map((tab) => (
              <button
                key={tab}
                className={`px-4 py-2 text-sm text-[#3B4154] border-b-2 border-transparent focus:outline-none ${
                  activeTab === tab ? "border-b-teal-500 font-bold" : ""
                }`}
                onClick={() => setActiveTab(tab)}
              >
                {tab}{" "}
                {tab === "Explore world data" &&
                  exploreData?.worldData?.count > 0 && (
                    <span>({exploreData?.worldData?.count})</span>
                  )}
                {tab === "Imported Data" && data?.datasets?.length > 0 && (
                  <span>({data?.pagination?.total})</span>
                )}
              </button>
            ))}
          </div>
          {activeTab === "Imported Data" && (
            <div className="flex items-center space-x-3">
              <Filter
                filters={data?.filters}
                selectedFilters={selectedFilters}
                setSelectedFilters={setSelectedFilters}
              />
              <Sort
                sortValue={sortValue}
                setSortValue={setSortValue}
                sortOrder={sortOrder}
                setSortOrder={setSortOrder}
              />
            </div>
          )}
        </div>
      </div>
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="p-4 text-sm">
            {activeTab === "Explore world data" && (
              <>
                {openExplore && exploreData?.worldData?.count > 0 ? (
                  <div>
                    {exploreData?.worldData?.data.map((dataset: any) => (
                      <div key={dataset.did}>
                        <Tile
                          title={dataset.title}
                          description={dataset.description}
                          tags={dataset.filetypes}
                          button_text="Import"
                          onButtonClick={() => {}}
                          id={dataset.did}
                          setUrlQuery={setUrlQuery}
                          isImportedData={dataset.isImported}
                        />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="mt-10">
                    {/* <h1 className="text-[20px] text-[#3B4154] font-[500]">Search to see the results</h1>
                    <p className="mt-[15px] text-[#666F8F] text-sm w-[450px]">
                      Explore the world's largest public data repository containing over 300,000+ datasets that can fit your needs.
                    </p> */}
                    <NoDataPlaceholder message="Search to see the results" />
                  </div>
                )}
              </>
            )}
            {activeTab === "Imported Data" && (
              <div className="mt-3">
                {data?.datasets?.map((dataset: any) => (
                  <div key={dataset._id} className="mt-5">
                    <Tile
                      title={dataset.title}
                      description={dataset.description}
                      tags={dataset.fileTypes}
                      button_text="View Dataset"
                      onButtonClick={() => handleViewDataset(dataset._id)}
                    />
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="flex justify-between items-center mt-5">
            {activeTab === "Explore world data" &&
              exploreData?.worldData?.data?.length > 0 && (
                <div className="text-gray-600 text-sm">
                  Showing {exploreData?.worldData?.data?.length} of{" "}
                  {exploreData?.worldData?.count}
                </div>
              )}
            {activeTab === "Imported Data" && data?.datasets?.length > 0 && (
              <div className="text-gray-600 text-sm">
                Showing {data?.datasets?.length} of {data?.pagination?.total}
              </div>
            )}
            {activeTab === "Imported Data" && data?.datasets?.length > 0 && (
              <Pagination
                totalPages={data?.pagination?.totalPages}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
              />
            )}
            {activeTab === "Explore world data" &&
              exploreData?.worldData?.data?.length > 0 && (
                <Pagination
                  totalPages={Math.ceil(
                    (exploreData?.worldData?.count ?? 0) / 10
                  )}
                  currentPage={currentPage}
                  setCurrentPage={setCurrentPage}
                />
              )}
          </div>
        </>
      )}
    </div>
  );
}
