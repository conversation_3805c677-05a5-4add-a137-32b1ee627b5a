import { useEffect } from "react";
import { XCircle, CheckCircle, X } from "lucide-react";

type ToastProps = {
  message: string;
  type?: "success" | "error";
  onClose: () => void;
  dismissTime?: number;
};

export const Toast: React.FC<ToastProps> = ({
  message,
  type = "success",
  onClose,
  dismissTime = 3000,
}) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, dismissTime);
    return () => clearTimeout(timer);
  }, [onClose, dismissTime]); // Include dismissTime in the dependency array

  return (
    <div
      className={`z-50 fixed top-16 right-5 flex items-center gap-3 px-4 py-3 rounded-lg shadow-md transition-opacity duration-300 
      ${type === "success"
          ? "text-white bg-teal-600"
          : "bg-red-500 text-white"
        }`}
    >
      {type === "success" ? (
        <CheckCircle className="w-5 h-5 text-white" />
      ) : (
        <XCircle className="w-5 h-5 text-white" />
      )}
      <span className="text-sm font-medium">{message}</span>
      <button onClick={onClose} className="text-white hover:text-gray-800">
        <X className="w-4 h-4" />
      </button>
    </div>
  );
};
