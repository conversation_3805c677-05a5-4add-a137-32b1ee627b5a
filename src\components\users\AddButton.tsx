"use client";
import React from "react";
import { FaPlus } from "react-icons/fa";

interface AddButtonProps {
  onClick: () => void;
  label: string;
}

export default function AddButton({ onClick, label }: AddButtonProps) {
  return (
    <div
      onClick={onClick}
      className="font-bold w-44 px-4 py-2 cursor-pointer rounded flex items-center justify-center bg-[var(--sidebar-item-selected)] text-white"
    >
      <FaPlus strokeWidth={0.5} size={14} className="mr-2" /> {label}
    </div>
  );
}
