import React, { useState, useEffect } from "react";
import Image from "next/image";
import AIAvatarToggle from "./ai_tab";
import CollaboratorTab from "./collaborator_tab";
import { Collaborator, ProjectDetails, User } from "@/service/types/types";
import GeneralTab from "./general_tab";
import { getCollabrators, getNonCollabrators } from "@/service/projects";
import { Toast } from "@/components/ui/toast/toast";
import Loader from "@/components/common/loader/loader";
import { useParams } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { setActiveManageTab } from "@/lib/store/features/project/projectManageSlice";
import Configurations from "./configurations";

export type ToastState = {
  message: string;
  type: "success" | "error";
} | null;

const ManageTab = ({
  projectDetails,
  fetchProjectDetails,
  isAiProject,
}: {
  projectDetails: ProjectDetails | null;
  fetchProjectDetails: () => void;
  isAiProject: boolean;
}) => {
  const dispatch = useDispatch();
  const activeTab = useSelector(
    (state: RootState) => state.manageTab.activeManageTab
  );
  const { id } = useParams() as { id: string };
  const [loading, setLoading] = useState<boolean>(false);
  //const [activeTab, setActiveTab] = useState("General");
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [nonCollaborators, setNonCollaborators] = useState<User[]>([]);
  const [toast, setToast] = useState<ToastState>(null);

  const sidebarOptions = [
    { name: "General", icon: "/assets/icons/general_icon_project.svg" },
    { name: "Collaborators", icon: "/assets/icons/collb-icon-project.svg" },
    { name: "AI Avatar", icon: "/assets/icons/avatar-icon.svg" },
  ];
  const AisidebarOptions = [
    { name: "General", icon: "/assets/icons/general_icon_project.svg" },
    { name: "Configuration", icon: "/assets/icons/configuration.svg" },
  ];

  const fetchCollaborators = async () => {
    try {
      setLoading(true);
      const res = await getCollabrators(id);
      setCollaborators(res.collaborators);
    } catch (error) {
      // console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const fetchNonCollaborators = async () => {
    try {
      setLoading(true);
      const response = await getNonCollabrators(id);
      setNonCollaborators(response.nonCollaborators);
    } catch (error) {
      // console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCollaborators();
    fetchNonCollaborators();
    fetchProjectDetails();
  }, []);

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="flex min-h-screen">
            {/* Sidebar */}
            <div className="w-1/5 bg-[#F4F5F6] py-4 border-r">
              <ul>
                {isAiProject ? (
                  <>
                    {AisidebarOptions.map((tab) => (
                      <li
                        key={tab.name}
                        className={` flex cursor-pointer p-2 text-[#3B4154] ${activeTab === tab.name
                          ? "bg-[#888FAA23] font-semibold border-l-[3px] border-l-[#00B2A1]"
                          : ""
                          }`}
                        onClick={() => dispatch(setActiveManageTab(tab.name))}
                      >
                        <Image
                          src={tab.icon}
                          alt={`${tab.name} icon`}
                          width={16}
                          height={16}
                          className="mr-2"
                        />
                        {tab.name == "Collaborators"
                          ? `Collaborators (${collaborators.length > 0
                            ? collaborators.length - 1
                            : 0
                          })`
                          : tab.name}
                      </li>
                    ))}
                  </>
                ) : (
                  <>
                    {sidebarOptions.map((tab) => (
                      <li
                        key={tab.name}
                        className={`flex cursor-pointer px-2 py-2 text-[#3B4154] border-l-[3px]
                          ${activeTab === tab.name
                            ? "bg-[#888FAA23] font-semibold border-l-[#00B2A1]"
                            : "border-transparent"
                          }`}
                        onClick={() => dispatch(setActiveManageTab(tab.name))}
                      >
                        <Image
                          src={tab.icon}
                          alt={`${tab.name} icon`}
                          width={16}
                          height={16}
                          className="mr-2"
                        />
                        {tab.name == "Collaborators"
                          ? `Collaborators (${collaborators.length > 0
                            ? collaborators.length - 1
                            : 0
                          })`
                          : tab.name}
                      </li>
                    ))}
                  </>
                )}
              </ul>
            </div>

            {/* Content area */}
            <div className="flex-1">
              {activeTab === "General" && (
                <GeneralTab projectDetails={projectDetails} fetchProjectDetails={fetchProjectDetails} />
              )}
              {activeTab === "Collaborators" && (
                <CollaboratorTab
                  projectDetails={projectDetails}
                  collaborators={collaborators}
                  nonCollaborators={nonCollaborators}
                  setToast={setToast}
                  fetchCollaborators={fetchCollaborators}
                  fetchNonCollaborators={fetchNonCollaborators}
                  fetchProjectDetails={fetchProjectDetails}
                />
              )}
              {activeTab === "AI Avatar" && (
                <AIAvatarToggle projectDetails={projectDetails} />
              )}
              {activeTab === "Configuration" && <Configurations projectDetails={projectDetails} fetchProjectDetails={fetchProjectDetails} />}
            </div>
          </div>
        </>
      )}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </>
  );
};

export default ManageTab;
