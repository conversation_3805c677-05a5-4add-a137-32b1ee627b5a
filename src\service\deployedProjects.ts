import { http } from "./methods";
import { ProjectDetails } from "./types/types";

export async function getDeployedProjects(): Promise<ProjectDetails[]> {
  try {
    const response = await http.get<{ projects: ProjectDetails[] }>(
      "/projects/deployed?page=1&limit=100"
    );
    return response.data.projects;
  } catch (error: any) {
    console.error(
      "Error fetching deployed projects:",
      error?.message || "Unknown error"
    );
    throw error;
  }
}
