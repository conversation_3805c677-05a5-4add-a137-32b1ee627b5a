import { http } from "./methods";

const filesService = {
  uploadFile: async (file: File, description: string, datasetId: string) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("description", description);
    formData.append("datasetId", datasetId);
    const res: any = await http.upload("/files/upload", formData);
    return res;
  },
};

export default filesService;
