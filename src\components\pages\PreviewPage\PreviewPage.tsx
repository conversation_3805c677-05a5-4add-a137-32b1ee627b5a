"use client";

import type React from "react";
import { <PERSON>, Download, <PERSON>rid, <PERSON><PERSON><PERSON>, <PERSON>, Filter, Maximize } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button/button";
import SearchInput from "@/components/ui/search-input/search-input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu/dropdown-menu";
import { useEffect, useRef, useState } from "react";
import { TabsContent } from "@radix-ui/react-tabs";
import FilePreview from "@/components/common/FilePreview/FilePreview";
import previewData from "@/service/preview-data";
import downloadData from "@/service/download-data";
import GraphPreview from "@/components/common/GraphView/GraphView";
import { parseStructuredData } from "@/lib/utils";
import MapPreview from "@/components/common/MapPreview/MapPreview";
import Loader from "@/components/common/loader/loader";

interface FileItem {
  filename: string;
  filesize?: number;
  url: string;
  description?: string;
  fileType?: string;
  isUrl?: boolean;
  _id?: string;
}

interface PreviewPageProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  filename: string;
  datasetId: string;
  description: string;
  dataset?: {
    _id?: string;
    title?: string;
    slug?: string;
    license?: string;
    views?: number;
    createdAt?: string;
    updatedAt?: string;
    fileTypes?: string[];
    files?: FileItem[];
  };
}

const PreviewPage: React.FC<PreviewPageProps> = ({ isOpen, setIsOpen, filename, datasetId, description, dataset }) => {
  const [searchValue, setSearchValue] = useState<string>("");
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [rowsPerPage, setRowsPerPage] = useState<number>(20);
  const [totalRows, setTotalRows] = useState<number>(0);
  const [page, setPage] = useState<number>(1);
  const [filters, setFilters] = useState<Record<string, string>>({});
  const [previewDataResponse, setPreviewDataResponse] = useState<any>({
    data: {
      data: [],
      pagination: { totalItems: 0, itemsPerPage: 20 },
    },
    status: 0,
  });
  const [headers, setHeaders] = useState<string[]>([]);
  const [parsedRows, setParsedRows] = useState<Record<string, any>[]>([]);
  const [loading, setLoading] = useState(true);
  const fullScreenContainerRef = useRef<HTMLDivElement>(null);
  const [searchPreviewDataRender, setSearchPreviewDataRender] = useState<number>(0);

  // Toggle filters for a given column
  const handleFilterChange = (columnName: string) => {
    setFilters((prev) => {
      const newFilters = { ...prev };
      if (columnName in newFilters) {
        delete newFilters[columnName];
      } else {
        newFilters[columnName] = searchValue || "";
      }
      return newFilters;
    });
  };

  // Parse headers/rows whenever previewDataResponse changes
  useEffect(() => {
    if (!previewDataResponse?.data?.data?.length) {
      setHeaders([]);
      setParsedRows([]);
      setTotalRows(0);
      setRowsPerPage(20);
      return;
    }
    const { theaders, tparsedRows } = parseStructuredData(previewDataResponse.data);
    setHeaders(theaders);
    setParsedRows(tparsedRows);
    setTotalRows(previewDataResponse.data.pagination.totalItems);
    setRowsPerPage(previewDataResponse.data.pagination.itemsPerPage);
  }, [filename, previewDataResponse]);

  // Enter fullscreen mode
  const handleEnterFullScreen = () => {
    if (fullScreenContainerRef.current?.requestFullscreen) {
      fullScreenContainerRef.current
        .requestFullscreen()
        .then(() => {
          setIsFullScreen(true);
          // setRowsPerPage(25);
        })
        .catch((err) => {
          console.error("Failed to enter fullscreen:", err);
        });
    } else {
      console.error("Fullscreen API is not supported.");
    }
  };

  // Download file
  async function handleDownloadClick() {
    try {
      const extension = filename.split(".").pop() ?? "";
      const mimeTypes: Record<string, string> = {
        pdf: "application/pdf",
        csv: "text/csv",
        xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        txt: "text/plain",
      };
      const contentType = mimeTypes[extension] ?? "application/octet-stream";
      const data = await downloadData(`${datasetId}/${filename}`);
      const blob = new Blob([data?.data], { type: contentType });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading file:", error);
    }
  }

  // Listen for fullscreen exit
  useEffect(() => {
    const handleFullScreenChange = () => {
      if (!document.fullscreenElement) {
        setIsFullScreen(false);
        setRowsPerPage(20);
      }
    };
    document.addEventListener("fullscreenchange", handleFullScreenChange);
    return () => {
      document.removeEventListener("fullscreenchange", handleFullScreenChange);
    };
  }, []);

  // Disable body scroll when the preview is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  // Fetch preview data whenever page, search, or filters change
  useEffect(() => {
    const fetchPreviewData = async () => {
      try {
        setLoading(true);
        const hasActiveFilters = Object.keys(filters).length > 0;
        const searchParam = hasActiveFilters ? "" : `search=${searchValue}`;
        const filterParam = `filters=${encodeURIComponent(JSON.stringify(filters))}`;
        const url = `${datasetId}/${filename}?${searchParam}&limit=${rowsPerPage}&start=${(page - 1) * rowsPerPage
          }&${filterParam}`;
        const fetchPreviewDataResponse = await previewData(url);
        setPreviewDataResponse(fetchPreviewDataResponse.data);
      } catch (e) {
        console.error(e);
      } finally {
        setLoading(false);
      }
    };
    fetchPreviewData();
  }, [page, filters, datasetId, filename, searchPreviewDataRender]);

  // Helpers for Additional Info
  const formatDate = (dateStr?: string) => {
    if (!dateStr) return "N/A";
    const dateObj = new Date(dateStr);
    return dateObj.toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatDaysAgo = (dateStr?: string) => {
    if (!dateStr) return "N/A";
    const dateObj = new Date(dateStr);
    const now = new Date();
    const diff = now.getTime() - dateObj.getTime();
    const diffDays = Math.floor(diff / (1000 * 60 * 60 * 24));
    return `${diffDays} days ago`;
  };

  // Basic fields from the dataset
  const dataLastUpdated = dataset?.updatedAt ? formatDate(dataset.updatedAt) : "N/A";
  const metadataLastUpdated = dataLastUpdated;
  const createdOn = dataset?.createdAt ? formatDate(dataset.createdAt) : "N/A";
  const createdDaysAgo = dataset?.createdAt ? formatDaysAgo(dataset.createdAt) : "N/A";
  const formatVal = dataset?.fileTypes && dataset.fileTypes.length > 0 ? dataset.fileTypes.join(", ") : "N/A";
  const licenseVal = dataset?.license || "No License Provided";
  const hasViews = dataset?.views && dataset.views > 0 ? "True" : "False";

  // Additional fields from previewDataResponse (no duplicates)
  const datastoreActiveVal = previewDataResponse?.data?.datastore_active ?? "No Data";
  const datastoreContainsAllVal = previewDataResponse?.data?.datastore_contains_all_records_of_source_file ?? "No Data";
  const idVal = previewDataResponse?.data?.id ?? "No Data";
  const lastModifiedVal = previewDataResponse?.data?.last_modified ?? "No Data";
  const metadataModifiedVal = previewDataResponse?.data?.metadata_modified ?? "No Data";
  const mimeTypeVal = previewDataResponse?.data?.mimetype ?? "No Data";
  const packageIdVal = previewDataResponse?.data?.package_id ?? "No Data";
  const positionVal = previewDataResponse?.data?.position ?? "No Data";
  const saveVal = previewDataResponse?.data?.save ?? "No Data";

  const fileItem = dataset?.files?.[0];
  const sizeVal = fileItem?.filesize ? `${(fileItem.filesize / 1024).toFixed(1)} KiB` : "No Data";
  const stateVal = fileItem ? "Active" : "No Data";
  const urlTypeVal = fileItem ? (fileItem.isUrl ? "URL" : "upload") : "No Data";

  const handleSearchBtnClicked = () => {
    if (Object.keys(filters).length > 0) {
      // console.log(filters);
      const updatedFilters = Object.keys(filters).reduce((acc, col) => {
        acc[col] = searchValue;
        return acc;
      }, {} as Record<string, string>);
      setFilters(updatedFilters);
    } else {
      setSearchPreviewDataRender((prev) => prev + 1);
    }
    setPage(1);
  };

  // console.log(filters);

  return (
    <div
      className={`fixed inset-y-0 right-0 z-50 h-screen w-[calc(100%-210px)] bg-white shadow-lg transform ${isOpen ? "translate-x-0" : "translate-x-full"
        } transition-transform duration-300 ease-in-out flex flex-col`}
    >
      {/* Header */}
      <div className="sticky top-0 z-10 flex h-14 items-center justify-between bg-[#3B4154] px-6">
        <h2 className="text-lg font-medium text-white">Dataset details</h2>
        <Button
          variant="ghost"
          size="icon"
          className="text-white hover:bg-white/20"
          onClick={() => {
            setIsOpen(false);
            setSearchValue("");
            setFilters({});
            setPage(1);
          }}
        >
          <X className="h-5 w-5" />
        </Button>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto p-6">
        {loading ? (
          <div className="h-full flex items-center justify-center">
            <Loader />
          </div>
        ) : (
          <>
            {/* Filename + Download */}
            <div className="mb-8 flex items-center justify-between">
              <h1 className="text-2xl font-semibold">{filename}</h1>
              <div className="flex gap-2">
                <Button variant="secondary" onClick={handleDownloadClick}>
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
              </div>
            </div>

            {/* File Description */}
            <div className="mb-8">
              <h3 className="mb-2 text-lg font-semibold text-[#3B4154]">File Description</h3>
              <p className="text-gray-600">{description}</p>
              <div className="mt-2 text-sm">
                <span className="font-semibold text-[#3B4154]">Source:</span> {filename}
              </div>
            </div>
            {/* Tabs: Grid, Graph, Map */}
            <Tabs defaultValue="grid" className="w-full">
              <div className="mb-4 flex items-center">
                <h3 className="mr-[25px] text-lg font-bold text-gray-700">Data Explorer</h3>
                <TabsList>
                  <TabsTrigger value="grid">
                    <Grid className="mr-2 h-4 w-4" />
                    Grid
                  </TabsTrigger>
                  <TabsTrigger value="graph">
                    <LineChart className="mr-2 h-4 w-4" />
                    Graph
                  </TabsTrigger>
                  <TabsTrigger value="map">
                    <Map className="mr-2 h-4 w-4" />
                    Map
                  </TabsTrigger>
                </TabsList>
              </div>

              {/* Search + Filters + Full Screen */}
              <div className="flex items-center justify-between gap-4 mb-5">
                <div className="relative flex items-center gap-2">
                  <SearchInput
                    onChange={(e) => setSearchValue(e.target.value)}
                    value={searchValue}
                    type="search"
                    placeholder="Search data"
                    onSearchClick={handleSearchBtnClicked}
                  />
                </div>
                <div className="flex gap-4">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button>
                        <Filter style={{ width: "16px" }} /> &nbsp; Filters
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-56">
                      {previewDataResponse?.data?.columnNames?.map((columnName: string) => (
                        <div key={columnName} className="px-2 py-2">
                          <DropdownMenuCheckboxItem
                            checked={columnName in filters}
                            onCheckedChange={() => handleFilterChange(columnName)}
                          >
                            {columnName}
                          </DropdownMenuCheckboxItem>
                        </div>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <div className="h-7 w-1 top-2 border-l-[0.5px] border-gray-200" />
                  <Button onClick={handleEnterFullScreen} className="flex items-center">
                    <Maximize style={{ width: "16px" }} /> &nbsp; Full Screen
                  </Button>
                </div>
              </div>

              {/* Grid View */}
              <TabsContent value="grid">
                <div
                  ref={fullScreenContainerRef}
                  className={
                    isFullScreen
                      ? "fixed inset-0 z-50 bg-white overflow-auto flex items-center justify-center"
                      : "rounded-lg border"
                  }
                >
                  <FilePreview
                    headers={headers}
                    parsedRows={parsedRows}
                    totalRows={totalRows}
                    page={page}
                    setPage={setPage}
                    rowsPerPage={rowsPerPage}
                    isFullScreen={isFullScreen}
                  />
                </div>
              </TabsContent>

              {/* Graph View */}
              <TabsContent value="graph" className="h-full overflow-y-auto">
                <div
                  ref={fullScreenContainerRef}
                  className={
                    isFullScreen
                      ? "fixed inset-0 z-50 bg-white overflow-auto flex items-center justify-center"
                      : "rounded-lg border h-full"
                  }
                >
                  <GraphPreview
                    parsedRows={parsedRows}
                    headers={headers}
                    filename={filename}
                    previewDataResponse={previewDataResponse}
                  />
                </div>
              </TabsContent>

              {/* Map View */}
              <TabsContent value="map">
                <div
                  ref={fullScreenContainerRef}
                  className={
                    isFullScreen
                      ? "fixed inset-0 z-50 bg-white overflow-auto flex items-center justify-center"
                      : "rounded-lg border h-full"
                  }
                >
                  <MapPreview
                    parsedRows={parsedRows}
                    headers={headers}
                    filename={filename}
                    previewDataResponse={previewDataResponse}
                  />
                </div>
              </TabsContent>
            </Tabs>

            {/* Additional Information Section */}
            <div className="mt-8">
              <h2 className="px-4 text-lg font-semibold text-[#3B4154]">Additional Information</h2>
              <div className="overflow-x-auto mt-4">
                <table className="min-w-full border-collapse">
                  <thead>
                    <tr>
                      <th className="px-4 py-2 text-left font-semibold text-[#3B4154]">Fields</th>
                      <th className="px-4 py-2 text-left font-semibold text-[#3B4154]">Values</th>
                    </tr>
                    {/* Horizontal line under headings */}
                    <tr>
                      <td colSpan={2} className="border-b border-gray-300"></td>
                    </tr>
                  </thead>
                  <tbody>
                    {/* Fields from dataset */}
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Data Last Updated</td>
                      <td className="px-4 py-2 text-gray-600">{dataLastUpdated}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Metadata Last Updated</td>
                      <td className="px-4 py-2 text-gray-600">{metadataLastUpdated}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Created</td>
                      <td className="px-4 py-2 text-gray-600">{createdOn}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Format</td>
                      <td className="px-4 py-2 text-gray-600">{formatVal}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">License</td>
                      <td className="px-4 py-2 text-gray-600">{licenseVal}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Created (Relative)</td>
                      <td className="px-4 py-2 text-gray-600">{createdDaysAgo}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Has Views</td>
                      <td className="px-4 py-2 text-gray-600">{hasViews}</td>
                    </tr>

                    {/* Info about the first file: Size, State, URL Type */}
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Size</td>
                      <td className="px-4 py-2 text-gray-600">{sizeVal}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">State</td>
                      <td className="px-4 py-2 text-gray-600">{stateVal}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">URL Type</td>
                      <td className="px-4 py-2 text-gray-600">{urlTypeVal}</td>
                    </tr>

                    {/* Additional fields from previewDataResponse */}
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Datastore Active</td>
                      <td className="px-4 py-2 text-gray-600">{datastoreActiveVal}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Datastore Contains All Records of Source File</td>
                      <td className="px-4 py-2 text-gray-600">{datastoreContainsAllVal}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">ID</td>
                      <td className="px-4 py-2 text-gray-600">{idVal}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Last Modified</td>
                      <td className="px-4 py-2 text-gray-600">{lastModifiedVal}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Metadata Modified</td>
                      <td className="px-4 py-2 text-gray-600">{metadataModifiedVal}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Mimetype</td>
                      <td className="px-4 py-2 text-gray-600">{mimeTypeVal}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Package ID</td>
                      <td className="px-4 py-2 text-gray-600">{packageIdVal}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Position</td>
                      <td className="px-4 py-2 text-gray-600">{positionVal}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-2 font-semibold text-[#3B4154]">Save</td>
                      <td className="px-4 py-2 text-gray-600">{saveVal}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            {/* End Additional Info */}
          </>
        )}
      </div>
    </div>
  );
};

export default PreviewPage;
