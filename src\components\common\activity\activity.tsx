"use client";
import React, { useEffect, useState } from "react";
import { <PERSON>d<PERSON><PERSON> } from "react-icons/md";

interface ActivityProps {
  data: {
    description: {
      parts: [any];
    };
    timestamp: string;
  };
  index: number;
  total: number;
}

const Activity: React.FC<ActivityProps> = ({ data, index, total }) => {
  const [parts, setParts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  useEffect(() => {
    if (data?.description?.parts) {
      setParts(data.description.parts);
    }
    setIsLoading(false);
  }, [data]);

  if (isLoading) return null;

  return (
    <div className="flex gap-4 items-center">
      <div className="relative">
        <div className="flex justify-center w-10 h-10 rounded-full bg-[#F4F5F6] mt-2">
          <img src="/assets/icons/description.svg" alt="#" width={15} />
        </div>
        {index !== total - 1 && <div className="absolute top-12 left-1/2 w-0.5 h-8 bg-gray-200 mt-1"></div>}
      </div>
      <div className=" w-full flex gap-2 items-center border-b-[1.5px] border-t-[1.5px]  rounded-md p-1 py-2">
        <div className="w-8 h-8 rounded-full bg-gray-500 ml-2">
          <img className="w-8 h-8 rounded-full" src={`${process.env.NEXT_PUBLIC_API_BASE_URL?.replace("/api", "")}${data.description.parts[0]?.thumbnail}`} alt="#" />
        </div>
        {parts.length >= 3 && (
          <div className="flex flex-col w-full">
            <div>
              <a href={typeof parts[0] === "object" ? parts[0].id : "#"}>
                <span className="hover:text-teal-500 text-[14px] text-[#333333]">
                  {typeof parts[0] === "object" ? parts[0].title : parts[0]}
                </span>
              </a>
              <span className="text-[#333333] text-[14px]">{parts[1]}</span>
              <a href={typeof parts[2] === "object" ? parts[2].id : "#"}>
                <span className="text-teal-500 hover:text-black text-[14px]">
                  {typeof parts[2] === "object" ? parts[2].title : parts[2]}
                </span>
              </a>
            </div>
            <div className="flex w-full justify-between items-center">
              <div className="text-[12px] text-[#888] flex justify-start">{data.timestamp}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Activity;
