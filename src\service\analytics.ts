import ReactGA from "react-ga4";

// Initialize Google Analytics
export const initializeGA = () => {
  const trackingId = process.env.NEXT_PUBLIC_GA_ID || "";
  ReactGA.initialize(trackingId);
};

// Track page views
export const trackPageView = (url: string) => {
  ReactGA.send({ hitType: "pageview", page: url });
};

// Track custom events (optional)
export const trackEvent = (category: string, action: string, label: string) => {
  ReactGA.event({
    category,
    action,
    label,
  });
};
