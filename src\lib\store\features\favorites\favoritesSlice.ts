"use client";
import { createSlice, createAsyncThunk, createAction } from "@reduxjs/toolkit";
import {
  getAllFavorites,
  addFavorite,
  removeFavorite,
} from "@/service/favorites";
import {
  FavoritesState,
  BookmarkItem,
  FavoriteItem,
  FavoriteToggleProject,
} from "@/service/types/types";

// Define a reset action for favorites
export const resetFavorites = createAction("favorites/resetFavorites");

const initialState: FavoritesState = {
  items: [],
  status: "idle",
  error: null,
};

export const fetchFavorites = createAsyncThunk(
  "favorites/fetchFavorites",
  async () => {
    const bookmarkItems: BookmarkItem[] = await getAllFavorites();
    // console.log("lelo---> " + bookmarkItems);
    return bookmarkItems.map((bm) => ({
      id: bm.projectId._id,
      name: bm.projectId.name || `Project ${bm.projectId._id}`,
      href: `/apps/deployed-projects/${bm.projectId._id}`,
      appUrl: bm.projectId.appUrl,
      isAiBox: bm.projectId.isAiBox,
    })) as FavoriteItem[];
  }
);

export const toggleFavorite = createAsyncThunk(
  "favorites/toggle",
  async ({
    project,
    isFavorite,
  }: {
    project: FavoriteToggleProject;
    isFavorite: boolean;
  }) => {
    console.log(project, "this is the project from fav");
    if (isFavorite) {
      await removeFavorite(project.id);
    } else {
      await addFavorite(project.id);
    }
    return {
      id: project.id,
      name: project.name,
      href: `/apps/deployed-projects/${project.id}`,
      isAiBox: project.isAiBox,
      appUrl: project.appUrl,
      isFavorite,
    };
  }
);

const favoritesSlice = createSlice({
  name: "favorites",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchFavorites.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchFavorites.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.items = action.payload;
      })
      .addCase(fetchFavorites.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message || "Failed to load favorites";
      })
      .addCase(toggleFavorite.pending, (state) => {
        state.status = "loading";
      })
      .addCase(toggleFavorite.fulfilled, (state, action) => {
        state.status = "succeeded";
        if (action.payload.isFavorite) {
          state.items = state.items.filter(
            (item) => item.id !== action.payload.id
          );
        } else {
          state.items.push({
            id: action.payload.id,
            name: action.payload.name,
            href: action.payload.href,
            isAiBox: action.payload.isAiBox,
            appUrl: action.payload.appUrl,
          });
        }
      })
      .addCase(toggleFavorite.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message || "Failed to update favorite";
      })
      .addCase(resetFavorites, (state) => {
        state.items = [];
        state.status = "idle";
        state.error = null;
      });
  },
});

export default favoritesSlice.reducer;
