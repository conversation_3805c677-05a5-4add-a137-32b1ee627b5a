import { DatasetResponse, ExploreDataResponse } from "@/types/dataset";
import { http, ApiResponse } from "./methods";

export type ImportedDataQuery = {
  page?: number;
  limit?: number;
  search?: string;
  tags?: string;
  license?: string;
  fileTypes?: string;
  sortBy?: string;
  sortOrder?: number;
};
export type ExploreDataQuery = {
  page?: number;
  limit?: number;
  query?: string;
};

export const dataService = {
  importWorldData: async (props: {
    orgId: string;
    token: string;
    datasetId: string;
  }): Promise<ApiResponse> => {
    try {
      const { orgId, token, datasetId } = props;
      const res = await http.post<ApiResponse>(
        "/external/import-youdata",
        { datasetId: datasetId },
        {
          headers: {
            organizationid: orgId,
            Cookie: token,
          },
        }
      );
      return res;
    } catch (error) {
      throw error;
    }
  },
  getImportedData: async (
    query: ImportedDataQuery
  ): Promise<ApiResponse<ApiResponse>> => {
    const {
      page = 1,
      limit = 10,
      search = "",
      tags = "",
      license = "",
      fileTypes = "",
      sortBy = "",
      sortOrder = 0,
    } = query;
    // Construct query parameters dynamically
    const params = new URLSearchParams();

    // Add parameters **only if they are not empty**
    if (search) params.append("search", search);
    if (tags) params.append("tags", tags);
    if (license) params.append("license", license);
    if (fileTypes) params.append("fileTypes", fileTypes);
    if (sortBy) params.append("sortBy", sortBy);
    if (sortOrder) params.append("sortOrder", String(sortOrder));
    if (page) params.append("page", String(page));
    if (limit) params.append("limit", String(limit));

    // Build the final API URL
    const url = `/datasets/world?${params.toString()}`;

    const response = await http.get<ApiResponse>(url);

    return response;
  },
  getCompanyData: async (
    query: ImportedDataQuery
  ): Promise<DatasetResponse> => {
    const {
      page = 1,
      limit = 10,
      search = "",
      tags = "",
      license = "",
      fileTypes = "",
      sortBy = "",
      sortOrder = 0,
    } = query;
    // Construct query parameters dynamically
    const params = new URLSearchParams();

    // Add parameters **only if they are not empty**
    if (search) params.append("search", search);
    if (tags) params.append("tags", tags);
    if (license) params.append("license", license);
    if (fileTypes) params.append("fileTypes", fileTypes);
    if (sortBy) params.append("sortBy", sortBy);
    if (sortOrder) params.append("sortOrder", String(sortOrder));
    if (page) params.append("page", String(page));
    if (limit) params.append("limit", String(limit));

    // Build the final API URL
    const url = `/datasets/company?${params.toString()}`;

    const response = await http.get<DatasetResponse>(url);

    return response.data;
  },
  getAllAssets: async (query: {
    page: number;
    limit: number;
    search: string;
    projectId: string;
  }): Promise<DatasetResponse> => {
    const { page = 1, limit = 10, search = "", projectId = "" } = query;
    // Construct query parameters dynamically
    const params = new URLSearchParams();

    // Add parameters **only if they are not empty**
    if (search) params.append("search", search);
    if (page) params.append("page", String(page));
    if (limit) params.append("limit", String(limit));
    if (projectId) params.append("projectId", String(projectId));
    try {
      const url = `/datasets/search?${params.toString()}`;
      const response = await http.get<DatasetResponse>(url);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  getExploredData: async (
    prop: ExploreDataQuery
  ): Promise<ExploreDataResponse> => {
    const { page = 1, limit = 10, query = "" } = prop;
    const params = new URLSearchParams();
    if (query) params.append("query", query);
    if (page) params.append("page", String(page));
    if (limit) params.append("limit", String(limit));
    const url = `/datasets/external?${params.toString()}`;
    const response = await http.get<ExploreDataResponse>(url);
    return response.data;
  },
  getDatasetById: async (id: string): Promise<ApiResponse> => {
    const res = await http.get<ApiResponse>(`/datasets/${id}`);
    return res;
  },
};
