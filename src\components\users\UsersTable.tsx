"use client";
import { User } from "@/service/userService";
import React from "react";
import { FaEdit } from "react-icons/fa";


interface UsersTableProps {
  users: User[];
  onEdit: (id: string) => void;
}

export default function UsersTable({ users, onEdit }: UsersTableProps) {
  return (
    <div className="overflow-y-auto max-h-[600px]">
      <table className="w-full">
        <thead className="sticky top-0 bg-white z-40">
          <tr className="text-left font-semibold text-[#3B4154] text-sm border-b border-[#CFD2DE]">
            <th className="px-2 py-4">Full Name</th>
            <th className="px-2 py-4">User Name</th>
            <th className="px-2 py-4">Email</th>
            <th className="px-2 py-4">Department</th>
            <th className="px-2 py-4">Edit</th>
          </tr>
        </thead>
        <tbody>
          {users.map(u => (

            <tr key={u._id} className="text-sm border-b border-[#CFD2DE]">
              <td className="p-2">{u.name}</td>
              <td className="p-2">{u.username}</td>
              <td className="p-2">{u.email}</td>
              <td className="p-2">{u.department.map(d => d.departmentId.name).join(", ")}</td>
              <td className="p-2">
                <FaEdit
                  size={16}
                  className="text-[#00B2A1] cursor-pointer"
                  onClick={() => onEdit(u._id)}
                />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
