"use client";

import Iframe from "@/components/common/Iframe/Iframe";
import { useEffect } from "react";

export default function Projects() {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="flex flex-col">
      <h1 className="text-xl px-4 mt-4">Governance Insights</h1>
      {process.env.NEXT_PUBLIC_GOVERNANCE_PAGE_URL && (
        <Iframe url={process.env.NEXT_PUBLIC_GOVERNANCE_PAGE_URL} />
      )}
    </div>
  );
}
