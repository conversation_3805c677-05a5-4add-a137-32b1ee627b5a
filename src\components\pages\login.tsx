import React, { useEffect, useState } from "react";
import { authService, userService } from "@/service/api";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { EyeIcon, EyeSlashIcon } from "@heroicons/react/24/solid";
import Checkbox from "@/components/ui/checkbox/page";
import { useDispatch, useSelector } from "react-redux";
import { login, logout, setUserInfo } from "@/lib/store/features/user/userSlice";
import { fetchFavorites } from "@/lib/store/features/favorites/favoritesSlice";
import type { AppDispatch } from "@/lib/store";
import { toast } from "react-toastify";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const dispatch = useDispatch<AppDispatch>();
  const user = useSelector((state: any) => state.user);
  const logo = process.env.NEXT_PUBLIC_USER_LOGO || "/sutra-logo.svg";

  // Example for Value Analytics main nav
  const isValueAnalyticsActive = pathname === "/";
  // Example for sub-pillars
  const isOperationalEfficiencyActive = pathname === "/operational-efficiency";
  // ...and so on for other pillars

  useEffect(() => {
    if (user.token) {
      router.push("/");
    }
    // Show toast if redirected from reset-password
    if (searchParams.get("reset") === "success") {
      toast.success("Password reset successfully");
      // Remove the query param from the URL so it doesn't show again on refresh
      const url = new URL(window.location.href);
      url.searchParams.delete("reset");
      window.history.replaceState({}, document.title, url.pathname);
    }
  }, []);

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await authService.login({ email, password });
      const duration = 6 * 60 * 60 * 1000; // 6 hours in milliseconds

      dispatch(login({ token: response.data.token, duration }));
      await dispatch(fetchFavorites() as any);
      try {
        const userResponse: any = await userService.getUser();
        dispatch(setUserInfo(userResponse.data));
      } catch (userError) {}

      // Always redirect to landing page after login
      router.push("/");
    } catch (err) {
      setError("Invalid email or password. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <ToastContainer />
      <div
        className="flex flex-col md:flex-row h-screen w-full"
        style={{
          fontFamily: "var(--sidebar-font-family)",
        }}
      >
        {/* Left Section (Login Form) */}
        <div className="w-full md:w-[40%] bg-white flex flex-col justify-center items-center p-6 md:p-12">
          <header className="flex items-center justify-center w-full h-12 mt-[25px] mb-[15px]">
            <div className="relative w-[150px] h-fit">
              <img src={logo} alt="Logo" style={{ objectFit: "contain", width: "100%", height: "100%" }} />
            </div>
          </header>

          <form className="w-full max-w-sm mt-8 md:mt-16" onSubmit={handleLogin}>
            <h2 className="text-3xl text-center font-medium text-[#3B4154] mb-8 mt-[15px]">Login</h2>

            {error && <p className="text-red-500 text-sm mb-3">{error}</p>}

            <label className="block text-[#666F8F] text-sm mb-2">Username or email</label>
            <input
              type="text"
              className="w-full border border-[#CFD2DE] rounded-[4px] text-[#3B4154] text-sm px-3 py-2 mb-[20px] focus:border-[#00B2A1]  outline-none h-[35px]"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email or username"
            />

            {/* Password Field with Toggle */}
            <label className="block text-[#666F8F] text-sm mb-2">Password</label>
            <div className="relative w-full mb-[20px]">
              <input
                type={showPassword ? "text" : "password"}
                className="w-full border border-[#CFD2DE] text-[#3B4154] text-sm rounded-[4px] px-3 py-2 pr-10 focus:border-[#00B2A1] outline-none h-[35px]"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-3 flex items-center text-gray-500 hover:text-[#00B2A1]"
              >
                {showPassword ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />}
              </button>
            </div>

            {/* Remember Me Checkbox */}
            <div className="flex items-center mb-4">
              <Checkbox
                id="dontShowAgain"
                label="Remember me"
                checked={dontShowAgain}
                onChange={() => setDontShowAgain(!dontShowAgain)}
              />
            </div>

            <button
              type="submit"
              className="w-full bg-[#00B2A1] text-white py-2 rounded-md hover:bg-[#009688] disabled:opacity-50 h-[45px] mt-[35px]"
              disabled={loading}
            >
              {loading ? "Logging in..." : "Login"}
            </button>

            {/* Forgot Password */}
            <div className="mt-2 text-right">
              <a href="/forgot" className="text-[#00B2A1] text-sm hover:underline">
                Forgot your password?
              </a>
            </div>
          </form>
        </div>

        {/* Right Section (Background Image) */}
        <div
          className="hidden md:flex md:w-[60%] bg-black flex-col justify-between text-white relative"
          style={{
            backgroundImage: "url('/assets/Mask Group 12.png')",
            backgroundSize: "cover",
          }}
        >
          <div className="flex-grow"></div>
          <div className="mx-24 mb-24">
            <h2 className="text-[32px] font-[700] mb-6 text-[#C4D5FD]">AI for Business Builders.</h2>
            <p className="text-[#CFD2DE] text-[18px] font-[400] leading-tight">
              Discover and actualize innovative new business opportunities and applications, driving ongoing substantial
              business growth.
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Login;
