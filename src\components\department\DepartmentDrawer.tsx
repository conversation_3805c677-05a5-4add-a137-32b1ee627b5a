"use client";
import React, { useEffect, useState } from "react";
import { X } from "lucide-react";
import departmentService, { Department } from "@/service/departments";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

interface DepartmentDrawerProps {
  mode: "add" | "edit";
  isOpen: boolean;
  dept?: Department;
  onClose: () => void;
  onSave: () => void;
}

export default function DepartmentDrawer({
  mode,
  isOpen,
  dept,
  onClose,
  onSave,
}: DepartmentDrawerProps) {
  const [form, setForm] = useState({
    name: "",
    description: "",
  });

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (mode === "edit" && dept) {
      setForm({
        name: dept.name,
        description: dept.description,
      });
    } else {
      setForm({ name: "", description: "" });
    }
  }, [mode, dept, isOpen]);

  useEffect(() => {
    document.body.style.overflow = isOpen ? "hidden" : "";
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setForm((f) => ({ ...f, [e.target.name]: e.target.value }));
  };

  const handleSubmit = async () => {
    setLoading(true);
    if (form.name.trim() === "") {
      toast.error("name is mandatory");
      setLoading(false);
      return;
    }
    if (form.description.trim() === "") {
      toast.error("description is mandatory");
      setLoading(false);
      return;
    }

    if (mode === "add") {
      try {
        const depData = {
          "name": form.name,
          "description": form.description,
          "thumbnailUrl": "string"
        };
        const res = await departmentService.createDepartment(depData);
        console.log(res);
        toast.success("Department created successfully!");
        setLoading(false);
        onClose();
      } catch (error: any) {
        console.error(error);
        toast.error(error.data.message || "something went wrong, try again");
      } finally {
        setLoading(false);
      }
    } else {
      try {
        const depData = {
          "name": form.name,
          "description": form.description,
          "thumbnailUrl": "string"
        };
        const res = await departmentService.updateDepartment(dept?._id || "", depData);
        console.log(res);
        toast.success("Department updated successfully!");
        setLoading(false);
        onClose();
      } catch (error: any) {
        console.error(error);
        toast.error(error.data.message || "something went wrong, try again");
      } finally {
        setLoading(false);
      }
    }
    onSave();
    onClose();
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar
        closeOnClick
        pauseOnHover
        draggable
        toastStyle={{ zIndex: 99999 }}
      />
      {/* overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={onClose}
        />
      )}

      {/* drawer */}
      <div
        className={`fixed top-0 right-0 h-full w-[40%] bg-white shadow-lg z-50 transform transition-transform duration-300 ease-in-out
          ${isOpen ? "translate-x-0" : "translate-x-full"}
          focus:outline-none focus:ring-2 focus:ring-[#00B2A1]`}
        tabIndex={-1}
      >
        {/* HEADER */}
        <div
          className="flex items-center justify-between p-4"
          style={{ backgroundColor: "#3B4154" }}
        >
          <h2 className="text-lg font-medium text-white">
            {mode === "add" ? "Add Department" : "Edit Department"}
          </h2>
          <button onClick={onClose} className="p-1 focus:outline-none focus:ring-2 focus:ring-[#00B2A1]">
            <X className="w-6 h-6 text-white" />
          </button>
        </div>

        {/* FORM */}
        <div className="p-6 overflow-y-auto space-y-4">
          {/* Name Field */}
          <div>
            <label className="block text-sm font-medium mb-1">Department Name  <span className="text-red-500">*</span></label>
            <input
              name="name"
              value={form.name}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#00B2A1]"
              type="text"
            />
          </div>

          {/* Description Field */}
          <div>
            <label className="block text-sm font-medium mb-1">Description  <span className="text-red-500">*</span></label>
            <textarea
              name="description"
              value={form.description}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#00B2A1]"
              rows={4}
            />
          </div>
        </div>

        {/* ACTIONS */}
        <div className="flex justify-end p-4 border-t space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border rounded text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#00B2A1]"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={loading}
            className="px-4 py-2 bg-[#00B2A1] text-white rounded focus:outline-none focus:ring-2 focus:ring-[#00B2A1] flex items-center justify-center min-w-[120px]"
          >
            {loading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              mode === "add" ? "Create Department" : "Save Changes"
            )}
          </button>
        </div>
      </div>
    </>
  );
}
