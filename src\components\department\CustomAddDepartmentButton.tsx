"use client";
import React from "react";
import { FaPlus } from "react-icons/fa";

interface CustomAddDepartmentButtonProps {
  onClick: () => void;
}

export default function CustomAddDepartmentButton({ onClick }: CustomAddDepartmentButtonProps) {
  return (
    <button
      type="button"
      onClick={onClick}
      className=" font-bold w-56 px-4 py-2 flex items-center justify-center bg-[var(--sidebar-item-selected)] text-white rounded cursor-pointer"
    >
      <FaPlus strokeWidth={0.5} size={14} className="mr-2" />
      Add Department
    </button>
  );
}
