import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { valueAnalyticsService } from "@/service/api";

export interface ValuePillar {
  _id: string;
  name: string;
  about: string;
  dollarType: string;
  createdAt: string;
  updatedAt: string;
}

interface ValuePillarsState {
  pillars: ValuePillar[];
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
}

const initialState: ValuePillarsState = {
  pillars: [],
  status: 'idle',
  error: null,
};

export const fetchValuePillars = createAsyncThunk(
  'valuePillars/fetchValuePillars',
  async () => {
    const response = await valueAnalyticsService.getValuePillars();
    return response.data.data as ValuePillar[];
  }
);

const valuePillarsSlice = createSlice({
  name: 'valuePillars',
  initialState,
  reducers: {
    resetValuePillars: (state) => {
      state.pillars = [];
      state.status = 'idle';
      state.error = null;
    },
    setValuePillars: (state, action) => {
      state.pillars = action.payload;
      state.status = 'succeeded';
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchValuePillars.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchValuePillars.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.pillars = action.payload;
      })
      .addCase(fetchValuePillars.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message || null;
      });
  }
});

export const { resetValuePillars, setValuePillars } = valuePillarsSlice.actions;

export default valuePillarsSlice.reducer;

export const selectValuePillarIdByName = (state: any, name: string) => {
  const pillar = state.valuePillars.pillars.find((p: ValuePillar) => p.name === name);
  return pillar ? pillar._id : null;
}; 