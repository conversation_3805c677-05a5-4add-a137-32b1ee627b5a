"use client"

import { MessageSquareText } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/pages/project/Comments/comments-component/comment-UI/button"

interface CommentButtonProps {
  count: number
  onClick: () => void
}

export function CommentButton({ count, onClick }: CommentButtonProps) {
  return (
    <Button variant="outline" className="flex items-center gap-1" onClick={onClick}>
      <MessageSquareText className="h-4 w-4" />
      <span className="font-medium">{count}</span>
      <span className="">Comments</span>
    </Button>
  )
}

