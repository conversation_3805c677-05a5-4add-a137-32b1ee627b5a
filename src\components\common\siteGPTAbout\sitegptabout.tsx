"use client";

import React from "react";

type AboutAppModalProps = {
    onClose: () => void;
};

export default function SiteGPTAbout({
    onClose,
}: AboutAppModalProps) {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg max-w-[80vw] w-full">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold text-gray-800">About Site GPT</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        ✕
                    </button>
                </div>
                <div className="px-6 py-10 text-gray-800" style={{ maxHeight: "70vh", overflowY: "auto" }}>
                    <div className="text-3xl font-bold mb-6">🤖 Site GPT</div>

                    <section className="mb-10">
                        <h2 className="text-2xl font-semibold mb-2">Welcome to Site GPT!</h2>
                        <p>
                            <strong>Site GPT</strong> is a powerful, purpose-built AI chatbot solution designed specifically for corporate websites. It enables organizations to create custom AI assistants trained directly on their website content, improving user engagement and providing fast, accurate responses to site visitors.
                        </p>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">🔍 What You Can Do with Site GPT</h2>
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-semibold">Ingest Website Content with One Click</h3>
                                <p>
                                    Simply provide your company’s main URL or TLD. Site GPT will automatically crawl, extract, and process your website content — no manual setup required.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Generate a Custom AI Assistant</h3>
                                <p>
                                    Based on the ingested content, Site GPT creates a chatbot trained specifically for your organization. It understands your services, products, and brand voice.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Manage Processed URLs</h3>
                                <p>
                                    View and manage the list of processed domains and sub-pages. This transparency helps you track what content was used to train your assistant.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Share Public Chatbots</h3>
                                <p>
                                    Create secure, shareable links so external users can access your chatbot. Perfect for support portals, microsites, or public-facing demos.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Embed on Your Website</h3>
                                <p>
                                    Easily integrate the chatbot into your corporate site via an iframe embed. No heavy dev work needed — just copy and paste.
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">💼 Business Value</h2>
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-semibold">AI Chat Without the Setup</h3>
                                <p>
                                    No need for complex integrations or content feeding — just point to your website and let Site GPT do the rest.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Enhance Visitor Experience</h3>
                                <p>
                                    Engage users with a knowledgeable, responsive chatbot that reflects your actual website content and services.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Speed Up Customer Support</h3>
                                <p>
                                    Let the assistant handle common questions based on your own site’s knowledge base, reducing the support team's workload.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Scale Across Departments</h3>
                                <p>
                                    Use Site GPT for HR portals, customer FAQs, investor relations — any department that has a webpage can have its own assistant.
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">🚀 How to Use Site GPT</h2>
                        <ol className="list-decimal list-inside space-y-2">
                            <li><strong>Step 1:</strong> Enter your website’s main URL (e.g., <code>https://yourcompany.com</code>)</li>
                            <li><strong>Step 2:</strong> Site GPT automatically crawls and processes your site content</li>
                            <li><strong>Step 3:</strong> Review processed domains and sub-pages in the URL Management dashboard</li>
                            <li><strong>Step 4:</strong> Interact with your generated chatbot to validate performance</li>
                            <li><strong>Step 5:</strong>
                                <ul className="list-disc ml-5 space-y-1">
                                    <li>Share via <strong>public link</strong>, or</li>
                                    <li>Embed using <strong>iframe code</strong> on your website</li>
                                </ul>
                            </li>
                            <li><strong>Step 6:</strong> Monitor usage and reprocess content when your site updates</li>
                        </ol>
                    </section>

                    <p className="text-lg font-semibold">Start engaging your visitors with intelligent chat today!</p>
                </div>
                <div className="mt-6 flex justify-end">
                    <button
                        onClick={onClose}
                        className="bg-[#00B2A1] text-white px-4 py-2 rounded-md hover:bg-[#00A090]"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
}
