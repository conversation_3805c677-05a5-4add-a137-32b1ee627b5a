"use client";

import { useRef } from "react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { ChevronRight } from "lucide-react";
import { AIBoxModel } from "@/service/types/types";
import { getAiBoxes, getAiBoxResponse } from "@/service/aiServices";
import Loader from "@/components/common/loader/loader";

export default function AIBoxSection() {
  const scrollRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [aiBox, setAiBox] = useState<getAiBoxResponse | null>(null);

  // Fetch projects from API
  useEffect(() => {
    const fetchAIProjects = async () => {
      try {
        setLoading(true);
        const aiData = await getAiBoxes();
        setAiBox(aiData);
      } catch (err: any) {
        // console.log(err);
      } finally {
        setLoading(false);
      }
    };
    fetchAIProjects();
  }, []);

  return (
    <div className="bg-[#F5F5F5] relative w-full  mx-auto px-8 py-4">
      {loading ? (
        <Loader />
      ) : (
        <>
          {/* header section */}
          <div className="text-lg text-[#3B4154] font-semibold">Discover AI-in-a-Box Projects</div>
          <div className="text-sm text-[#666F8F] mb-4 mt-1">
            Explore hundreds of pre-built generative AI projects for your business and deploy them effortlessly — no
            coding required.
          </div>
          <div ref={scrollRef} className="flex flex-nowrap overflow-x-auto scroll-smooth gap-4 p-2 no-scrollbar">
            {aiBox?.data.map((card, index) => (
              <div
                key={index}
                onClick={() => router.push(`/projects/ai-project/${card._id}`)}
                className="min-w-[13rem] max-w-[16rem] bg-white p-4 rounded-[15px] border border-[#CFD2DE] cursor-pointer hover:border-[#00B2A1]"
              >
                <img className="object-cover" src={card.thumbnailUrl} height={50} width={50} alt="Thumbnail" />
                <h3 className="font-semibold text-lg mt-2 mb-1">{card.name}</h3>
                <p className="text-gray-500 text-sm mt-1">{card.description}</p>
              </div>
            ))}
            {aiBox && aiBox.data && aiBox?.data?.length > 6 && (
              <div
                onClick={() => router.push("/projects/all-ai-projects")}
                className="min-w-[150px] flex flex-col items-center justify-center cursor-pointer text-white transition"
              >
                <div className="w-6 h-6 flex items-center justify-center bg-teal-500 rounded-full">
                  <ChevronRight className="w-4 h-4" />
                </div>
                <span className="mt-2 text-sm font-medium text-teal-500">View all</span>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
