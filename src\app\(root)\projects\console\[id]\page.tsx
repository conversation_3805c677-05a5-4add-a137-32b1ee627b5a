"use client";

import { useSidebar } from "@/context/sidebar-context";
import { setProjectActiveTab } from "@/lib/store/features/project/projectTabSlice";
import { getDataAssets, getDataSageUrl } from "@/service/projects";
import Image from "next/image";
import { useSearchParams, useParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { RiExpandDiagonalSLine } from "react-icons/ri";
import { useDispatch } from "react-redux";


export default function ProjectConsolePage() {
  const params = useParams();
  const searchParams = useSearchParams();

  const projectName = searchParams.get("name") ?? "Unknown Project";
  const appUrl = decodeURIComponent(searchParams.get("consoleUrl") ?? "#");
  const appType = searchParams.get("appType") ?? "Unknown App";

  const router = useRouter();
  const { toggleSidebar, isOpen } = useSidebar();
  const [isFullScreen, setIsFullScreen] = useState<boolean>(false);
  const [dataSageUrl, setDataSageUrl] = useState<string>("");
  const ideIframeRef = useRef<HTMLIFrameElement>(null);
  const dataModelerIframeRef = useRef<HTMLIFrameElement>(null);
  const dispatch = useDispatch();
  const [mode, setMode] = useState<'AI Builder' | 'Data Modeler'>('AI Builder');

  // Read the env variable (must be string comparison for frontend)
  const isDataSageEnabled = process.env.NEXT_PUBLIC_DATA_SAGE === 'true';

  const handleFullScreen = () => {
    const currentIframe = mode === 'AI Builder' ? ideIframeRef.current : dataModelerIframeRef.current;
    if (currentIframe) {
      if (!document.fullscreenElement) {
        currentIframe.requestFullscreen();
      } else {
        document.exitFullscreen();
      }
    }
  };

  const handleDeployment = () => {
    router.push(`/projects/dashboard/${params.id}`);
    dispatch(setProjectActiveTab("Deployment"));
    toggleSidebar();
  };

  useEffect(() => {
    const onFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", onFullScreenChange);

    return () => {
      document.removeEventListener("fullscreenchange", onFullScreenChange);
    };
  }, []);

  useEffect(() => {
    const fetchDataSageUrl = async () => {
      const dataSageUrl = await getDataSageUrl();
      setDataSageUrl(dataSageUrl.data);
    };
    fetchDataSageUrl();
  }, []);

  return (
    <div className="min-h-screen" style={{ backgroundColor: "var(--background)", color: "var(--foreground)" }}>
      <div className="w-[100%]">
        <div className="mb-2 mt-[6px] ml-[15px] pb-2 text-2xl flex items-center justify-between mr-[15px]">
          <div className="flex items-center gap-4">
            <Image
              src="/assets/keyboard_backspace_24dp_FILL0_wght300_GRAD0_opsz24 (1).svg"
              alt="Back"
              width={19}
              height={20}
              className="cursor-pointer"
              onClick={() => {
                router.back();
                router.back();
                if (!isOpen) {
                  toggleSidebar();
                }
              }}
            />
            <h1
              style={{
                font: "normal normal medium 20px/29px Lato",
                letterSpacing: "0px",
                color: "#3B4154",
                textAlign: "left",
                opacity: 1,
              }}
            >
              {projectName}
            </h1>
          </div>
          <div className="flex items-center gap-4">
            {appType == "Dashboard" ? (
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2 cursor-pointer" onClick={handleDeployment}>
                  <Image src="/assets/icons/dep_new_icon.svg" alt="deploy" width={15} height={15} />
                  <div className="text-[14px] text-[#00B2A1]"> Deployment</div>
                </div>
              </div>
            ) : (<></>)}
            {appType !== "Dashboard" && isDataSageEnabled ? (
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <div className="flex bg-gray-100 rounded-md">
                    <button
                      className={`px-3 py-1 rounded-md text-sm font-semibold transition-colors duration-150 ${mode === 'AI Builder' ? 'bg-[#00B2A1] text-white' : 'bg-transparent text-[#00B2A1]'}`}
                      style={{ border: 'none' }}
                      onClick={() => setMode('AI Builder')}
                    >
                      AI Builder
                    </button>
                    <button
                      className={`px-3 py-1 rounded-md text-sm font-semibold  transition-colors duration-150 ml-2 ${mode === 'Data Modeler' ? 'bg-[#00B2A1] text-white' : 'bg-transparent text-[#00B2A1]'}`}
                      style={{ border: 'none' }}
                      onClick={() => setMode('Data Modeler')}
                    >
                      Data Modeler
                    </button>
                  </div>
                </div>
              </div>
            ) : null}
            {!isFullScreen && (
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2 cursor-pointer" onClick={handleFullScreen}>
                  <RiExpandDiagonalSLine className="text-[#00B2A1] font-[300] opacity-70" />
                  <div className="text-[14px] text-[#00B2A1]"> Full Screen</div>
                </div>
              </div>
            )}
          </div>

        </div>

        {/* Toggleable iframes, always mounted, only one visible at a time */}
        <div className="mt-1 border rounded-sm overflow-hidden relative" style={{ height: "88vh" }}>
          {/* IDE iframe */}
          <iframe
            src={appUrl}
            title="Project App"
            className={`w-full h-full absolute top-0 left-0 ${mode === 'AI Builder' ? 'block' : 'hidden'}`}
            allowFullScreen
            ref={ideIframeRef}
          />
          {/* Data Modeler iframe */}
          <iframe
            src={dataSageUrl}
            title="Data Modeler"
            className={`w-full h-full absolute top-0 left-0 ${mode === 'Data Modeler' ? 'block' : 'hidden'}`}
            allowFullScreen
            ref={dataModelerIframeRef}
          />
        </div>
      </div>
    </div>
  );
}
