"use client";

import React, { useState } from 'react';
import { OpportunityData } from '../utils/types';

interface ProgressBarProps {
  value: number;
  max: number;
  color: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ value, max, color }) => {
  const percentage = (value / max) * 100;

  return (
    <div className="w-full bg-gray-200 rounded-full h-2">
      <div
        className={`h-2 rounded-full ${color}`}
        style={{ width: `${percentage}%` }}
      ></div>
    </div>
  );
};

interface OpportunitiesListProps {
  opportunities: OpportunityData[];
  onSelectOpportunity: (opportunity: OpportunityData) => void;
  selectedOpportunityId?: number;
}

const OpportunitiesList: React.FC<OpportunitiesListProps> = ({
  opportunities,
  onSelectOpportunity,
  selectedOpportunityId
}) => {
  const [sortField, setSortField] = useState<keyof OpportunityData>('valueScore');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [filterCategory, setFilterCategory] = useState<string>('');

  // Get unique categories for filter
  const categories = Array.from(new Set(opportunities.map(opp => opp.category)));

  // Sort opportunities
  const sortedOpportunities = [...opportunities].sort((a, b) => {
    const aValue = a[sortField] ?? 0;
    const bValue = b[sortField] ?? 0;
    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });
  // Filter opportunities
  const filteredOpportunities = filterCategory
    ? sortedOpportunities.filter(opp => opp.category === filterCategory)
    : sortedOpportunities;

  const handleSort = (field: keyof OpportunityData) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Default to desc for new field
      setSortField(field);
      setSortDirection('desc');
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div className="p-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
        <h2 className="text-lg font-medium text-[#111827]">Opportunities List</h2>

        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Filter by:</span>
            <select
              className="border border-gray-300 rounded-md px-3 py-1.5 text-sm"
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Sort by:</span>
            <select
              className="border border-gray-300 rounded-md px-3 py-1.5 text-sm"
              value={`${sortField}-${sortDirection}`}
              onChange={(e) => {
                const [field, direction] = e.target.value.split('-');
                setSortField(field as keyof OpportunityData);
                setSortDirection(direction as 'asc' | 'desc');
              }}
            >
              <option value="valueScore-desc">Value (High to Low)</option>
              <option value="valueScore-asc">Value (Low to High)</option>
              <option value="readinessScore-desc">Readiness (High to Low)</option>
              <option value="readinessScore-asc">Readiness (Low to Low)</option>
              <option value="name-asc">Name (A-Z)</option>
              <option value="name-desc">Name (Z-A)</option>
            </select>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                #
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Opportunity
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
                Value
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
                Readiness
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                Category
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                Data Sources
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredOpportunities.map((opportunity) => (
              <tr
                key={opportunity.id}
                className={`
                  hover:bg-gray-50
                  ${selectedOpportunityId === opportunity.id ? 'bg-[#F1F5F9]' : ''}
                `}
              >
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {opportunity.id}
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm font-medium text-gray-900 cursor-pointer hover:text-[#00B2A1]"
                    onClick={() => onSelectOpportunity(opportunity)}>
                    {opportunity.name}
                  </div>
                  <div className="text-sm text-gray-500 mt-1 pr-4">{opportunity.description}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 mb-1">{opportunity.valueScore.toFixed(1)}</div>
                  <ProgressBar value={opportunity.valueScore} max={10} color="bg-[#3b82f6]" />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 mb-1">{opportunity.readinessScore.toFixed(1)}</div>
                  <ProgressBar value={opportunity.readinessScore} max={10} color="bg-[#8b5cf6]" />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 py-1 inline-flex text-xs leading-5 font-medium rounded-full bg-[#F1F5F9] text-gray-700">
                    {opportunity.category}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {opportunity.dataSources}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex gap-2">
                    <button
                      className="px-3 py-1.5 text-xs bg-[#00B2A1] text-white rounded-md hover:bg-[#00A090]"
                    >
                      Add to Project
                    </button>
                    <button
                      className="px-3 py-1.5 text-xs border border-gray-300 rounded-md hover:bg-gray-50"
                      onClick={() => onSelectOpportunity(opportunity)}
                    >
                      View Details
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default OpportunitiesList;