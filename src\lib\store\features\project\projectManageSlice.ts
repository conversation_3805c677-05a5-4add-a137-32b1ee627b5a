import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const manageTabs = ["General", "Collaborators", "AI Avatar", "Configuration"];

interface TabState {
  activeManageTab: string;
}

// Create a slice for tab management
const tabSlice = createSlice({
  name: "manageTabs",
  initialState: {
    activeManageTab: "General",
  } as TabState,
  reducers: {
    setActiveManageTab: (state, action: PayloadAction<string>) => {
      if (manageTabs.includes(action.payload)) {
        state.activeManageTab = action.payload;
      }
    },
  },
});

// Export actions
export const { setActiveManageTab } = tabSlice.actions;
export default tabSlice.reducer;
