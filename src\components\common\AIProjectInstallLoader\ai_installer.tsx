import React from 'react';
import Image from 'next/image';
import { FadeLoader } from 'react-spinners';
import { useAIInstall } from '@/service/hooks/useAIInstall';


interface IntallAIProjectLoaderProps {
    showModal: boolean;
    handleRunBackground: () => void;
    handleComplete: () => void;
}

const AIInstallModal: React.FC<IntallAIProjectLoaderProps> = ({
    showModal,
    handleRunBackground,
    handleComplete

}) => {
    if (!showModal) return null;
    const latestMessage = useAIInstall();
    const extractedMessage = latestMessage.message.split(":")[0].trim();
    if (latestMessage.message === "Deployment completed successfully.") {
        handleComplete();
    }
    return (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex justify-center items-center">
            <div className="bg-white p-6 rounded-[4px] shadow-lg max-w-sm w-full">
                <div className="flex justify-center">
                    <FadeLoader
                        height={10}
                        margin={-5}
                        radius={0}
                        width={2}
                    />
                </div>
                <div className='text-center text-lg mt-3 text-[#1B1D21]'>
                    Processing…
                </div>
                <div className='text-center text-sm mt-3 text-[#666F8F]'>
                    Estimated time: 3-4 mins
                </div>

                <div className='text-center text-sm mt-4 text-[#3B4154]'>
                    {extractedMessage || "Processing..."}
                </div>

                <button className="w-full text-center text-[#00B2A1] text-lg mt-6 mb-4"
                    onClick={handleRunBackground}
                >
                    Run in Background
                </button>
            </div>
        </div>
    );
};

export default AIInstallModal;
