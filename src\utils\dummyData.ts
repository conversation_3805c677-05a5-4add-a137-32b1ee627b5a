export const dummyValueAnalyticsData = {
  projects: [
    {
      id: "project-1",
      name: "Cloud Migration",
      description: "Migrating on-premise infrastructure to cloud",
      valuePillars: [
        {
          id: "pillar-1",
          name: "Cost Efficiency",
          benefits: [
            {
              id: "benefit-1",
              name: "Infrastructure Cost Reduction",
              metrics: [
                {
                  id: "metric-1",
                  name: "Server Cost Savings",
                  dollarValue: 75000,
                  dollarType: "Hard"
                },
                {
                  id: "metric-2",
                  name: "Maintenance Reduction",
                  dollarValue: 25000,
                  dollarType: "Hard"
                }
              ]
            }
          ]
        },
        {
          id: "pillar-2",
          name: "Operational Efficiency",
          benefits: [
            {
              id: "benefit-2",
              name: "Reduced Downtime",
              metrics: [
                {
                  id: "metric-3",
                  name: "Productivity Improvement",
                  dollarValue: 45000,
                  dollarType: "Soft"
                }
              ]
            }
          ]
        }
      ]
    },
    {
      id: "project-2",
      name: "Process Automation",
      description: "Automating manual business processes",
      valuePillars: [
        {
          id: "pillar-3",
          name: "Productivity",
          benefits: [
            {
              id: "benefit-3",
              name: "Time Savings",
              metrics: [
                {
                  id: "metric-4",
                  name: "Labor Cost Reduction",
                  dollarValue: 120000,
                  dollarType: "Hard"
                }
              ]
            }
          ]
        },
        {
          id: "pillar-4",
          name: "Quality Improvement",
          benefits: [
            {
              id: "benefit-4",
              name: "Error Reduction",
              metrics: [
                {
                  id: "metric-5",
                  name: "Rework Avoidance",
                  dollarValue: 35000,
                  dollarType: "Soft"
                }
              ]
            }
          ]
        }
      ]
    },
    {
      id: "project-3",
      name: "AI Implementation",
      description: "Implementing AI for customer service",
      valuePillars: [
        {
          id: "pillar-5",
          name: "Customer Experience",
          benefits: [
            {
              id: "benefit-5",
              name: "Response Time Improvement",
              metrics: [
                {
                  id: "metric-6",
                  name: "Customer Retention",
                  dollarValue: 85000,
                  dollarType: "Soft"
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  monthlyAggregation: [
    { month: "Jan", value: 25000 },
    { month: "Feb", value: 35000 },
    { month: "Mar", value: 42000 },
    { month: "Apr", value: 48000 },
    { month: "May", value: 53000 },
    { month: "Jun", value: 60000 },
    { month: "Jul", value: 68000 },
    { month: "Aug", value: 75000 },
    { month: "Sep", value: 82000 },
    { month: "Oct", value: 90000 },
    { month: "Nov", value: 95000 },
    { month: "Dec", value: 105000 }
  ]
};

export const dummyFilteredData = {
  projects: dummyValueAnalyticsData.projects.slice(0, 2),
  monthlyAggregation: dummyValueAnalyticsData.monthlyAggregation.slice(0, 6)
};
