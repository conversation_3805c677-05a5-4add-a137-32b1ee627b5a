"use client";

import Iframe from "@/components/common/Iframe/Iframe";
import { useSearchParams } from "next/navigation";

const AIHealth = () => {
    const searchParams = useSearchParams();
    const projectName = searchParams.get("projectName");
    const AIHealthUrl = process.env.NEXT_PUBLIC_AI_HEALTH_PAGE_URL || "";
    const finalURl = `${AIHealthUrl}?project=${projectName}`;
    return (
        <div>
            <div className="text-xl px-6 py-6">Project AI Health</div>
            {AIHealthUrl && <Iframe url={finalURl} className="h-[calc(100vh-6.3rem)]" />}
        </div>
    );
};
export default AIHealth;
