import React from 'react';
import Image from 'next/image';


interface AIAvatarDisableModalProps {
    showModal: boolean;
    setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
    dontShowAgain: boolean;
    setDontShowAgain: React.Dispatch<React.SetStateAction<boolean>>;
    handleConfirmDisable: () => void;
}

const AIAvatarDisableModal: React.FC<AIAvatarDisableModalProps> = ({
    showModal,
    setShowModal,
    dontShowAgain,
    setDontShowAgain,
    handleConfirmDisable
}) => {
    if (!showModal) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex justify-center items-center">
            <div className="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full">
                <div className="flex justify-center">
                    <Image
                        src="/assets/icons/warning_avatar_icon.svg"
                        alt="Warning Icon"
                        width={30}
                        height={30}
                    />
                </div>
                <p className="text-center text-base mt-4">
                    Disabling the AI Avatar will require you to complete the entire AI modeling and testing process.
                </p>
                <p className="text-center text-sm text-gray-500 mt-3">
                    You can enable it again later from the manage tab.
                </p>

                <div className="flex items-center mt-6">
                    <input
                        type="checkbox"
                        id="dontShowAgain"
                        checked={dontShowAgain}
                        onChange={() => setDontShowAgain(!dontShowAgain)}
                        className="h-5 w-5 mr-2 appearance-none border-2 border-gray-400 rounded-sm checked:border-[#00B2A1] checked:bg-white checked:after:content-[''] checked:after:border-b-2 checked:after:border-r-2 checked:after:border-[#00B2A1] checked:after:w-2 checked:after:h-3 checked:after:block checked:after:rotate-45 checked:after:translate-x-[5px] checked:after:translate-y-[-1px] focus:outline-none focus:ring-0 relative"
                    />
                    <label htmlFor="dontShowAgain" className="text-sm">
                        Do not show this message again
                    </label>
                </div>

                <div className="flex justify-end mt-4 gap-4">
                    <button
                        onClick={handleConfirmDisable}

                        className="text-[#666] text-sm"
                    >
                        Yes, Disable it.
                    </button>
                    <button
                        onClick={() => setShowModal(false)}
                        className="bg-[#00B2A1] text-white px-4 py-2 rounded-md text-sm"
                    >
                        No, Cancel
                    </button>
                </div>
            </div>
        </div>
    );
};

export default AIAvatarDisableModal;
