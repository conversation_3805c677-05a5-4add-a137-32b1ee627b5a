import React, { useState, useEffect } from "react";
import ManageTab, { ToastState } from "@/components/pages/project/tabs/manage";
import { useParams } from "next/navigation";
import { AIBoxModel, AIProject } from "@/service/types/types";
import { useRouter } from "next/navigation";
import { getAiDetails, installAI } from "@/service/aiServices";
import AIInstallModal from "@/components/common/AIProjectInstallLoader/ai_installer";
import AIInstallSuccessModal from "@/components/common/AIProjectSuccessInstaller/ai_installer_success";
import Loader from "@/components/common/loader/loader";
import { Toast } from "@/components/ui/toast/toast";


const ProjectDashboard = () => {
  //const { id } = useParams() as { id: string };
  const params = useParams();
  const projectIdParam = params.aiProjectId ?? params.id;

  // Ensure it's a string (not an array or undefined)
  const id: string | null = Array.isArray(projectIdParam)
    ? projectIdParam[0]
    : projectIdParam ?? "";

  const tabs = ["README", "Changelog"];
  const [aiProjectDetails, setAiProjectDetails] = useState<AIProject | null>(
    null
  );
  const [loading, setLoading] = useState<boolean>(true);
  const [installing, setInstalling] = useState<string>("in-process");
  const [toast, setToast] = useState<ToastState>(null);
  const [showModal, setShowModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const router = useRouter();

  const fetchProjectDetails = async () => {
    if (!id) return;
    try {
      const response = await getAiDetails(id);
      // console.log(response, "this is whole response");
      setAiProjectDetails(response.aiProject);
      // console.log(response.install, "this is the respoinse");
      if (response.install === "") {
        setInstalling("new");
      } else if (typeof response.install === "object" && response.install?.status) {
        setInstalling(response.install.status);
      }
    } catch (error) {
      console.error("Error fetching project details:", error);
    } finally {
      setLoading(false);
    }
  };

  const parseMarkdown = (text: string | undefined) => {
    if (!text) return "";
    return text
      .replace(/^# (.*$)/gim, "<h1>$1</h1>") // H1
      .replace(/^## (.*$)/gim, "<h2>$1</h2>") // H2
      .replace(/^### (.*$)/gim, "<h3>$1</h3>") // H3
      .replace(/\*\*(.*?)\*\*/gim, "<b>$1</b>") // Bold
      .replace(/\*(.*?)\*/gim, "<i>$1</i>") // Italic
      .replace(/`([^`]+)`/g, "<code>$1</code>") // Inline Code
      .replace(/\n/g, "<br>") // Convert remaining newlines to <br>
      .replace(/\n\n+/g, "\n"); // Convert multiple newlines to a single newline
  };

  const getTimeDifference = (dateString: string | undefined): string => {
    if (!dateString) return "";
    const updatedDate = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor(
      (now.getTime() - updatedDate.getTime()) / 1000
    );

    const secondsInMinute = 60;
    const secondsInHour = 60 * secondsInMinute;
    const secondsInDay = 24 * secondsInHour;
    const secondsInMonth = 30 * secondsInDay;
    const secondsInYear = 12 * secondsInMonth;

    if (diffInSeconds < secondsInMinute) {
      return `${diffInSeconds} second${diffInSeconds !== 1 ? "s" : ""} ago`;
    } else if (diffInSeconds < secondsInHour) {
      const minutes = Math.floor(diffInSeconds / secondsInMinute);
      return `${minutes} minute${minutes !== 1 ? "s" : ""} ago`;
    } else if (diffInSeconds < secondsInDay) {
      const hours = Math.floor(diffInSeconds / secondsInHour);
      return `${hours} hour${hours !== 1 ? "s" : ""} ago`;
    } else if (diffInSeconds < secondsInMonth) {
      const days = Math.floor(diffInSeconds / secondsInDay);
      return `${days} day${days !== 1 ? "s" : ""} ago`;
    } else if (diffInSeconds < secondsInYear) {
      const months = Math.floor(diffInSeconds / secondsInMonth);
      return `${months} month${months !== 1 ? "s" : ""} ago`;
    } else {
      const years = Math.floor(diffInSeconds / secondsInYear);
      return `${years} year${years !== 1 ? "s" : ""} ago`;
    }
  };

  const formatDate = (dateString: string | undefined): string => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `Created in ${month}/${day}/${year}`;
  };

  useEffect(() => {
    fetchProjectDetails();
  }, [id]);
  const handleInstall = async () => {
    setInstalling("in-process");
    try {
      const response = await installAI(id);
      setShowModal(true);
    } catch (error: any) {
      setShowModal(false);
      setToast({
        message: error?.data?.message || "Failed to install project",
        type: "error",
      });
      console.error("Error installing project:", error);
    }
  }

  return (
    <div className="p-8 font-[Lato]">
      {loading ? (
        <Loader />
      ) : (
        <div className="">
          <div className="flex items-center gap-5">

            <img
              src={aiProjectDetails?.thumbnailUrl || ""}
              alt={aiProjectDetails?.name || ""}
              width={60}
              height={60}
              className="bg-white"
            />
            <div className="flex flex-col gap-2">
              <h1 className="text-xl">{aiProjectDetails?.name}</h1>
              <button
                className={`w-32 py-1 rounded-[3px] text-sm ${installing === 'new' ? "bg-teal-500 text-white" :
                  installing === 'inProgress' ? "bg-[#CED2DE] cursor-not-allowed text-[#888FAA]" :
                    installing === 'error' ? "bg-teal-500 text-white" :
                      "bg-[#CED2DE] cursor-not-allowed text-[#888FAA]"
                  }`}
                onClick={handleInstall}
                disabled={installing === 'in-process' || installing === 'success'}
              >
                {installing === 'new' ? "Add to Projects" :
                  installing === 'in-process' ? "Processing..." :
                    installing === 'error' ? "Re-install" :
                      "Added to Projects"
                }
              </button>
            </div>
          </div>
          <div className="flex gap-20 justify-between">
            <div className="w-3/4">
              <div className="text-[#3b4154] mt-5 text-sm ">
                {aiProjectDetails?.description}
              </div>
              <div className="font-semibold text-[#3B4154] text-xl mt-5 border-b pb-2 border-[#CFD2DE]">
                Readme
              </div>
              <div className="h-full">
                {/* {activeTab === "README" && ( */}
                <div
                  className="prose max-w-full mt-5 text-[#3B4154]"
                  dangerouslySetInnerHTML={{
                    __html: parseMarkdown(aiProjectDetails?.readme),
                  }}
                />
              </div>
            </div>
            <div className="w-1/4 flex flex-col gap-4 mt-5 text-[#3B4154]">
              <h1 className="text-[#3B4154] font-semibold">Project Metrices</h1>
              <div className="flex gap-4">
                <img src="/assets/Group_7093.svg" alt="" width={30} />
                <p>{formatDate(aiProjectDetails?.createdAt)}</p>
              </div>
              <div className="flex gap-4">
                <img src="/assets/Group_7093.svg" alt="" width={30} />
                <p>Modified {getTimeDifference(aiProjectDetails?.updatedAt)}</p>
              </div>
              <h1 className="text-[#3B4154] font-semibold">Category</h1>
              <div className="flex justify-between items-center">
                <span className="flex flex-wrap gap-2">
                  {aiProjectDetails?.tags?.map((tag: any, index: number) => (
                    <span
                      key={index}
                      className="px-3 py-1 text-sm bg-[#f4f5f6] text-gray-700 rounded-md"
                    >
                      {tag.name}
                    </span>
                  ))}
                </span>
              </div>
            </div>
          </div>
          <AIInstallModal
            showModal={showModal}
            handleRunBackground={() => {
              setShowModal(false);
            }}
            handleComplete={() => {
              setShowModal(false);
              setShowSuccessModal(true);
            }}
          />
          <AIInstallSuccessModal
            showModal={showSuccessModal}
            handleDone={() => {
              setShowSuccessModal(false);
              router.push(`/projects`);
            }}
          />
          {toast &&
            <Toast message={toast.message}
              type={toast.type}
              onClose={() => setToast(null)}
              dismissTime={5000}
            />
          }

        </div>
      )}
    </div>
  );
};

export default ProjectDashboard;
