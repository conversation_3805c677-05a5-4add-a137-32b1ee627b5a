import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface runInBg {
  isRunningInBg: boolean;
}

const initialState: runInBg = {
  isRunningInBg: false,
};

const runInBgSlice = createSlice({
  name: "runInBg",
  initialState,
  reducers: {
    setRunningInBgStatus: (
      state,
      action: PayloadAction<{ status: boolean }>
    ) => {
      state.isRunningInBg = action.payload.status;
    },
  },
});

export const { setRunningInBgStatus } = runInBgSlice.actions;
export default runInBgSlice.reducer;
