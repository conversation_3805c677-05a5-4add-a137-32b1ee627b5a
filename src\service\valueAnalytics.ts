import { AxiosError } from "axios";
import { http } from "./methods";

const getValueAnalyticsData = async (params: any): Promise<any> => {
  const queryParams = new URLSearchParams();
  if (params.projectId) {
    queryParams.append("projectId", params.projectId);
  }
  if (params.startDate) {
    queryParams.append("startDate", params.startDate);
  }
  if (params.endDate) {
    queryParams.append("endDate", params.endDate);
  }
  try {
    // console.log("params", queryParams.toString());
    const response = await http.get(
      `/value-analytics/list?${queryParams.toString()}`
    );
    // console.log("response", response.data);

    return {
      data: response.data as { data: Array<Record<string, any>> },
      status: response.status,
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    throw {
      data: axiosError.response?.data,
      status: axiosError.response?.status || 500,
      message: axiosError.message,
    };
  }
};

export default getValueAnalyticsData;
